# ViewDataTemplate - Reusable Master Data Component

## Overview

The `ViewDataTemplate` is a comprehensive, reusable React component that extracts all the UI/UX patterns from the `customer.jsx` implementation and makes them completely data-agnostic. This template eliminates code duplication across master data components while maintaining the exact look, feel, and functionality of the original customer implementation.

## Features

### Complete UI Structure
- **AppContainer Layout**: Full responsive layout with sidebar support
- **Multiple View Modes**: Cards, Compact, List, and Table views
- **Search & Filtering**: Advanced search with multiple filter operators
- **Sorting & Grouping**: Column-based sorting and grouping capabilities
- **Selection Management**: Single and multi-select with bulk operations
- **Pagination Support**: Built-in pagination for large datasets

### Form & Dialog Patterns
- **Multi-tab Forms**: Support for complex forms with multiple tabs
- **Dynamic Field Rendering**: Configurable form fields with validation
- **Modal Dialogs**: Add/Edit/View dialogs with consistent styling
- **Stepper Navigation**: Visual progress indication for multi-step forms

### Data Visualization
- **Chart Integration**: Chart.js integration with multiple chart types
- **Summary Cards**: Configurable summary statistics cards
- **Activity Logging**: Built-in activity tracking and display

### AdminComponents Integration
- **Consistent Styling**: Uses all AdminComponents for consistent UI
- **Theme Support**: Full Material-UI theme integration
- **Responsive Design**: Mobile-friendly responsive layouts

## Quick Start

### 1. Create a Configuration Object

```javascript
import { CheckCircle, Cancel, People } from '@mui/icons-material';

const myEntityConfig = {
    entityName: 'Product',
    displayField: 'name',
    statusField: 'status',
    searchFields: ['name', 'category', 'description'],
    
    columns: [
        { key: 'name', label: 'Product Name', type: 'string', groupable: true },
        { key: 'category', label: 'Category', type: 'string', groupable: true },
        { key: 'price', label: 'Price', type: 'number', groupable: false },
        { key: 'status', label: 'Status', type: 'status', groupable: true }
    ],
    
    actions: {
        view: true,
        edit: true,
        delete: true
    },
    
    initialData: [
        { id: 1, name: 'Product A', category: 'Electronics', price: 299.99, status: 'Active' },
        { id: 2, name: 'Product B', category: 'Clothing', price: 49.99, status: 'Inactive' }
    ]
};
```

### 2. Create Your Component

```javascript
import React from 'react';
import ViewDataTemplate from './viewDataTemplate';
import { myEntityConfig } from './myEntityConfig';

const Products = () => {
    return <ViewDataTemplate config={myEntityConfig} />;
};

export default Products;
```

That's it! You now have a fully functional master data component with all the features of the customer implementation.

## Configuration Reference

### Basic Configuration

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `entityName` | string | Yes | Name of the entity (e.g., 'Customer', 'Product') |
| `displayField` | string | Yes | Primary field to display in logs and references |
| `statusField` | string | No | Field used for status-based filtering and badges |
| `searchFields` | string[] | Yes | Fields to search when using the search box |
| `defaultSortColumn` | string | No | Default column to sort by |

### Column Configuration

```javascript
columns: [
    {
        key: 'name',              // Field key (supports nested: 'contact.email')
        label: 'Product Name',    // Display label
        type: 'string',          // Data type: 'string', 'number', 'status', 'avatar'
        groupable: true          // Whether this column can be grouped
    }
]
```

### Form Configuration

#### Simple Form (Single Tab)
```javascript
formSections: [
    {
        title: 'Basic Information',
        fields: [
            {
                key: 'name',
                label: 'Name *',
                type: 'text',
                required: true,
                xs: 12,
                md: 6
            }
        ]
    }
]
```

#### Multi-Tab Form
```javascript
formTabs: [
    {
        label: 'Basic Info',
        type: 'form',
        sections: [/* form sections */]
    },
    {
        label: 'Related Data',
        type: 'tabView',
        entityName: 'RelatedItem',
        columns: [/* column definitions */],
        data: [],
        onAdd: () => console.log('Add related item')
    }
]
```

### Field Types

| Type | Description | Additional Props |
|------|-------------|------------------|
| `text` | Text input | `multiline`, `rows` |
| `email` | Email input | - |
| `phone` | Phone input | - |
| `number` | Number input | `endAdornment` |
| `select` | Dropdown select | `optionsKey`, `options` |
| `checkbox` | Checkbox input | - |

### Actions Configuration

```javascript
actions: {
    view: true,     // Enable view action
    edit: true,     // Enable edit action
    delete: true    // Enable delete action
},

customActions: [
    {
        key: 'customAction',
        label: 'Custom Action',
        icon: MyIcon,
        handler: (item, { setData, addLog, setModalState }) => {
            // Custom action logic
        }
    }
]
```

### Status Configuration

```javascript
statusOptions: [
    {
        value: 'Active',
        label: 'Active',
        variant: 'active',
        icon: CheckCircle
    },
    {
        value: 'Inactive',
        label: 'Inactive',
        variant: 'inactive',
        icon: Cancel
    }
]
```

### Chart Configuration

```javascript
chartData: (item) => ({
    labels: ['Jan', 'Feb', 'Mar'],
    datasets: [{
        label: 'Sales',
        data: [12, 19, 3],
        backgroundColor: 'rgba(54, 162, 235, 0.2)'
    }]
}),
chartTitle: 'Sales Analytics'
```

## Advanced Features

### Custom Action Handlers
Custom actions receive the selected item and utility functions:

```javascript
handler: (item, { setData, addLog, setModalState }) => {
    // Update data
    setData(prevData => /* modify data */);
    
    // Log activity
    addLog('Performed custom action');
    
    // Open custom modal
    setModalState({ isOpen: true, item, mode: 'custom' });
}
```

### Nested Field Support
The template supports nested object fields using dot notation:

```javascript
{ key: 'contact.email', label: 'Contact Email' }
{ key: 'address.street', label: 'Street Address' }
```

### Dropdown Data
Provide dropdown options for select fields:

```javascript
dropdownData: {
    categories: [
        { value: 'electronics', label: 'Electronics' },
        { value: 'clothing', label: 'Clothing' }
    ]
}
```

## Migration from Existing Components

To migrate an existing component to use the template:

1. **Extract Data Structure**: Identify your data fields and create column definitions
2. **Map Form Fields**: Convert your form fields to the template's field configuration
3. **Configure Actions**: Define which actions are available and any custom actions
4. **Set Up Status Options**: If you have status fields, configure the status options
5. **Test & Refine**: Test all functionality and adjust configuration as needed

## Examples

See `exampleConfig.js` for comprehensive examples of:
- Customer configuration (complex multi-tab form)
- Employee configuration (simple single-tab form)

See `Employees.jsx` for a simple implementation example.

## Benefits

- **Consistent UI/UX**: All components use the same patterns and styling
- **Reduced Development Time**: New master data components can be created in minutes
- **Maintainable Code**: Changes to UI patterns only need to be made in one place
- **Feature Rich**: All advanced features (search, filtering, charts) available out of the box
- **Type Safety**: Configuration-driven approach reduces runtime errors
