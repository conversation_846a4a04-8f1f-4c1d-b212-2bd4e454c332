{"name": "adminportal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cross-env NODE_OPTIONS=--max-old-space-size=10240 vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview --base=/AdminPortal/"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fontsource/roboto": "^5.2.6", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.1.2", "chart.js": "^4.5.0", "exceljs": "^4.4.0", "framer-motion": "^12.19.1", "graceful-fs": "^4.2.11", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "cross-env": "^7.0.3", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}