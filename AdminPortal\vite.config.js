import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import fs from 'fs';
import gracefulFs from 'graceful-fs';
gracefulFs.gracefulify(fs);


// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  base: '/AdminPortal/',
  server: {
    port: 5174,
    host: true,
    open: '/AdminPortal/'
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false
  }
})
