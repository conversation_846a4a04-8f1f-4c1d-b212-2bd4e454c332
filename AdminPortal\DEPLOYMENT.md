# AdminPortal Deployment Guide

## Base Path Configuration

This application is configured to run with the base path `/AdminPortal/` to support hosting at URLs like:
- `https://namptest.hcltechswnp.com/AdminPortal/`

## Development

The application now runs at:
- **Local**: `http://localhost:5176/AdminPortal/`
- **Admin Panel**: `http://localhost:5176/AdminPortal/admin`

### Commands
```bash
npm run dev    # Start development server
npm run build  # Build for production
npm run preview # Preview production build
```

## Production Deployment

### 1. Build the Application
```bash
npm run build
```

### 2. Deploy to Web Server

#### For Apache Servers
- Copy the `dist` folder contents to your web server's `/AdminPortal/` directory
- The included `.htaccess` file will handle client-side routing
- Ensure Apache has `mod_rewrite` enabled

#### For Nginx Servers
Add this configuration to your nginx.conf:
```nginx
location /AdminPortal/ {
    alias /path/to/your/dist/;
    try_files $uri $uri/ /AdminPortal/index.html;
    index index.html;
}
```

#### For IIS Servers
Create a `web.config` file in the dist folder:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <rewrite>
            <rules>
                <rule name="AdminPortal" stopProcessing="true">
                    <match url=".*" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="/AdminPortal/index.html" />
                </rule>
            </rules>
        </rewrite>
    </system.webServer>
</configuration>
```

### 3. Verify Deployment
After deployment, verify these URLs work:
- `https://yourdomain.com/AdminPortal/` (Main application)
- `https://yourdomain.com/AdminPortal/admin` (Admin panel)

## Configuration Files Modified

1. **vite.config.js**: Added `base: '/AdminPortal/'`
2. **App.jsx**: Added `basename="/AdminPortal"` to Router
3. **package.json**: Updated preview script
4. **public/.htaccess**: Added for Apache routing support

## Notes

- All internal navigation uses React Router and will work correctly
- External links and redirects have been updated to use the new base path
- The application supports both development and production environments
- Static assets are automatically prefixed with the base path
