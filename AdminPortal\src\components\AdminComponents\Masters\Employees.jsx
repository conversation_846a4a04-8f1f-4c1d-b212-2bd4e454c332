import React from 'react';
import ViewDataTemplate from './viewDataTemplate';
import { employeeConfig } from './exampleConfig';

/**
 * Employee management component using ViewDataTemplate
 * 
 * This demonstrates how easy it is to create a new master data component
 * by simply importing the template and passing a configuration object.
 */
const Employees = () => {
    return <ViewDataTemplate config={employeeConfig} />;
};

export default Employees;
