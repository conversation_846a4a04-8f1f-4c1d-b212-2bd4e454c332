import React, { useState, useMemo, useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    Drawer, ToggleButton, Avatar, List, ListItem, ListItemText, FormControlLabel, Menu, MenuItem,
    Select, InputLabel, FormControl, Chip, Card, ListItemIcon, ToggleButtonGroup,
    TableContainer, CardContent, Divider, Dialog, DialogContent, DialogActions, Grid,
    Tabs, Tab, Accordion, AccordionSummary, AccordionDetails, LinearProgress,
    Stepper, Step, StepLabel, DialogTitle
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, MoreVert, Close, SearchOff,
    <PERSON><PERSON>hart, PieChart, DonutLarge, Settings, Save, Home, NavigateNext, ExpandMore, Check, NavigateBefore
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';

// --- GENERIC COMPONENTS ---
const TabPanel = ({ children, value, index }) => {
    return (
        <div role="tabpanel" hidden={value !== index} id={`tabpanel-${index}`} aria-labelledby={`tab-${index}`}>
            {value === index && <AdminComponents.TabPanelContainer>{children}</AdminComponents.TabPanelContainer>}
        </div>
    );
};

// Custom Step Icon Component for the Stepper
function StepIcon(props) {
    const { active, completed, className, icon } = props;
    return (
        <AdminComponents.StepIconRoot ownerState={{ active, completed }} className={className}>
            {completed ? <Check sx={{ color: 'var(--success-main)', fontSize: '1.2rem' }} /> : <AdminComponents.StepIconText>{icon}</AdminComponents.StepIconText>}
        </AdminComponents.StepIconRoot>
    );
}

// --- GENERIC ACTION COMPONENTS ---
const DialogActionButtons = ({ onView, onEdit, onDelete, item, config }) => (
    <Box onClick={e => e.stopPropagation()}>
        {config.actions?.view && <IconButton size="small" title="View Details" onClick={() => onView?.(item)}><Visibility fontSize="small" /></IconButton>}
        {config.actions?.edit && <IconButton size="small" title="Edit" onClick={() => onEdit?.(item)}><Edit fontSize="small" /></IconButton>}
        {config.actions?.delete && <IconButton size="small" color="error" title="Delete" onClick={() => onDelete?.([item.id])}><Delete fontSize="small" /></IconButton>}
    </Box>
);

const ActionButtons = ({ item, onView, onEdit, onDelete, onCustomAction, config, isCondensed }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleClick = (event) => {
        event.stopPropagation();
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => setAnchorEl(null);

    const handleAction = (action, event) => {
        event?.stopPropagation();
        action();
        handleClose();
    };

    if (isCondensed) {
        return (
            <Box>
                <IconButton size="small" onClick={handleClick}>
                    <MoreVert fontSize="small" />
                </IconButton>
                <Menu
                    anchorEl={anchorEl}
                    open={open}
                    onClose={handleClose}
                >
                    {config.actions?.view && (
                        <AdminComponents.ActionMenuItem onClick={(e) => handleAction(() => onView(item), e)}>
                            <Visibility fontSize="small" /> View
                        </AdminComponents.ActionMenuItem>
                    )}
                    {config.customActions?.map((action, index) => (
                        <AdminComponents.ActionMenuItem key={index} onClick={(e) => handleAction(() => onCustomAction(action.key, item), e)}>
                            {action.icon && <action.icon fontSize="small" />} {action.label}
                        </AdminComponents.ActionMenuItem>
                    ))}
                    {config.actions?.edit && (
                        <AdminComponents.ActionMenuItem onClick={(e) => handleAction(() => onEdit(item), e)}>
                            <Edit fontSize="small" /> Edit
                        </AdminComponents.ActionMenuItem>
                    )}
                    {config.actions?.delete && (
                        <AdminComponents.ErrorMenuItem onClick={(e) => handleAction(() => onDelete([item.id]), e)}>
                            <Delete fontSize="small" /> Delete
                        </AdminComponents.ErrorMenuItem>
                    )}
                </Menu>
            </Box>
        );
    }

    return (
        <Box display="flex" gap={0.5}>
            {config.actions?.view && <IconButton size="small" onClick={(e) => { e.stopPropagation(); onView(item); }}><Visibility fontSize="small" /></IconButton>}
            {config.customActions?.slice(0, 2).map((action, index) => (
                <IconButton key={index} size="small" onClick={(e) => { e.stopPropagation(); onCustomAction(action.key, item); }}>
                    {action.icon && <action.icon fontSize="small" />}
                </IconButton>
            ))}
            {config.actions?.edit && <IconButton size="small" onClick={(e) => { e.stopPropagation(); onEdit(item); }}><Edit fontSize="small" /></IconButton>}
            {config.actions?.delete && <IconButton size="small" color="error" onClick={(e) => { e.stopPropagation(); onDelete([item.id]); }}><Delete fontSize="small" /></IconButton>}
        </Box>
    );
};

// --- GENERIC CARD COMPONENTS ---
const GenericItemCard = ({ item, columns, isSelected, onSelect, config, onView, onEdit, onDelete, onCustomAction }) => (
    <AdminComponents.CardBase isSelected={isSelected}>
        <Checkbox className="card-checkbox" checked={isSelected} onChange={() => onSelect(item.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <DialogActionButtons onView={onView} onEdit={onEdit} onDelete={onDelete} item={item} config={config} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            {columns.slice(0, 4).map((col, index) => {
                const value = col.key.includes('.') ? 
                    col.key.split('.').reduce((obj, key) => obj?.[key], item) : 
                    item[col.key];
                
                return (
                    <Box key={col.key} display="flex" alignItems="center" gap={1}>
                        {index === 0 ? (
                            <Typography variant="h6" component="div" noWrap>{value}</Typography>
                        ) : (
                            <Typography variant="body2" color="text.secondary">{col.label}: {value}</Typography>
                        )}
                    </Box>
                );
            })}
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const GenericCompactCard = ({ item, columns, isSelected, onSelect, config, onView, onEdit, onDelete, onCustomAction }) => (
    <AdminComponents.CardBase isSelected={isSelected}>
        <Checkbox className="card-checkbox" checked={isSelected} onChange={() => onSelect(item.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <DialogActionButtons onView={onView} onEdit={onEdit} onDelete={onDelete} item={item} config={config} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            {columns.slice(0, 2).map((col, index) => {
                const value = col.key.includes('.') ? 
                    col.key.split('.').reduce((obj, key) => obj?.[key], item) : 
                    item[col.key];
                
                return (
                    <Box key={col.key} display="flex" alignItems="center" gap={1}>
                        {index === 0 ? (
                            <Typography variant="subtitle1" fontWeight="bold" noWrap>{value}</Typography>
                        ) : (
                            <Typography variant="caption" color="text.secondary">{value}</Typography>
                        )}
                    </Box>
                );
            })}
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const GenericListItem = ({ item, columns, isSelected, onSelect, config, onView, onEdit, onDelete, onCustomAction, isCondensed }) => (
    <AdminComponents.ListItemCard isSelected={isSelected}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isSelected} onChange={() => onSelect(item.id)} onClick={e => e.stopPropagation()} />
            {columns.slice(0, 4).map((col, index) => {
                const value = col.key.includes('.') ? 
                    col.key.split('.').reduce((obj, key) => obj?.[key], item) : 
                    item[col.key];
                
                if (index === 0) {
                    return (
                        <Box key={col.key} display="flex" alignItems="center" gap={1}>
                            <Box>
                                <Typography fontWeight="bold">{value}</Typography>
                                {columns[1] && (
                                    <Typography variant="body2" color="text.secondary">
                                        {columns[1].key.includes('.') ? 
                                            columns[1].key.split('.').reduce((obj, key) => obj?.[key], item) : 
                                            item[columns[1].key]}
                                    </Typography>
                                )}
                            </Box>
                        </Box>
                    );
                }
                
                if (index === 2 && config.statusField) {
                    const statusValue = config.statusField.includes('.') ? 
                        config.statusField.split('.').reduce((obj, key) => obj?.[key], item) : 
                        item[config.statusField];
                    
                    return (
                        <Box key={col.key} display="flex" alignItems="center" gap={1}>
                            <AdminComponents.StatusBadge ownerState={{ status: statusValue }} label={statusValue} size="small" />
                            {config.customActions?.[0] && (
                                <a
                                    href="#"
                                    style={{ color: '#1976d2', textDecoration: 'underline', fontSize: '0.98em', marginLeft: 8 }}
                                    onClick={e => { e.preventDefault(); e.stopPropagation(); onCustomAction(config.customActions[0].key, item); }}
                                >
                                    {config.customActions[0].label}
                                </a>
                            )}
                        </Box>
                    );
                }
                
                return (
                    <Typography key={col.key} variant="body2">{value}</Typography>
                );
            })}
            <AdminComponents.ListItemActions>
                <ActionButtons 
                    item={item} 
                    onView={onView} 
                    onEdit={onEdit} 
                    onDelete={onDelete} 
                    onCustomAction={onCustomAction}
                    config={config} 
                    isCondensed={isCondensed} 
                />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

// --- GENERIC TABLE COMPONENT ---
const GenericTable = ({ data, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, addLog, groupByKeys, onDelete, onEdit, onView, onCustomAction, config, isCondensed }) => {
    const dragItemIndex = useRef(null);
    const dragOverItemIndex = useRef(null);

    const handleDrop = () => {
        const dragItemContent = columnOrder[dragItemIndex.current];
        const dragOverItemContent = columnOrder[dragOverItemIndex.current];
        const dragItemIndex_copy = dragItemIndex.current;
        const dragOverItemIndex_copy = dragOverItemIndex.current;

        const newColumnOrder = [...columnOrder];
        newColumnOrder[dragItemIndex_copy] = dragOverItemContent;
        newColumnOrder[dragOverItemIndex_copy] = dragItemContent;
        setColumnOrder(newColumnOrder);
        addLog?.(`Reordered columns: moved ${config.columns.find(c => c.key === dragItemContent)?.label} to position ${dragOverItemIndex_copy + 1}`);
    };

    const renderCellContent = (item, colKey) => {
        const column = config.columns.find(c => c.key === colKey);
        const value = colKey.includes('.') ?
            colKey.split('.').reduce((obj, key) => obj?.[key], item) :
            item[colKey];

        if (column?.type === 'status' || colKey === config.statusField) {
            return <AdminComponents.StatusBadge ownerState={{ status: value }} label={value} size="small" />;
        }

        if (column?.type === 'avatar' && value) {
            return <Avatar src={value} sx={{ width: 32, height: 32 }} />;
        }

        return value;
    };

    const renderGroupedRows = (data, keys, level = 0) => {
        if (!keys.length || !data.length) {
            return data.map(item => (
                <TableRow key={item.id} hover selected={selectedId === item.id}>
                    <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(item.id)} onChange={() => onSelectOne(item.id)} /></TableCell>
                    {columnOrder.map(colKey => (
                        <AdminComponents.ContentTableCell key={colKey} onClick={() => onRowClick(item)}>
                            {renderCellContent(item, colKey)}
                        </AdminComponents.ContentTableCell>
                    ))}
                    <AdminComponents.ActionTableCell align="center" onClick={() => onRowClick(item)}>
                        <ActionButtons
                            item={item}
                            onView={onView}
                            onEdit={onEdit}
                            onDelete={onDelete}
                            onCustomAction={onCustomAction}
                            config={config}
                            isCondensed={isCondensed}
                        />
                    </AdminComponents.ActionTableCell>
                </TableRow>
            ));
        }

        const [currentKey, ...remainingKeys] = keys;
        const groups = data.reduce((acc, item) => {
            const groupValue = item[currentKey] || 'Ungrouped';
            if (!acc[groupValue]) acc[groupValue] = [];
            acc[groupValue].push(item);
            return acc;
        }, {});

        return Object.entries(groups).map(([groupValue, groupItems]) => [
            <AdminComponents.GroupHeaderRow key={`group-${groupValue}`}>
                <AdminComponents.GroupHeaderCell colSpan={columnOrder.length + 2} level={level}>
                    <Typography variant="subtitle2" fontWeight="bold">
                        {config.columns.find(c => c.key === currentKey)?.label}: {groupValue} ({groupItems.length})
                    </Typography>
                </AdminComponents.GroupHeaderCell>
            </AdminComponents.GroupHeaderRow>,
            ...renderGroupedRows(groupItems, remainingKeys, level + 1)
        ]).flat();
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <AdminComponents.ResponsiveTable stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < data.length} checked={data.length > 0 && selectedIds.length === data.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, index) => (
                            <AdminComponents.DraggableHeaderCell
                                key={colKey}
                                draggable
                                onDragStart={() => (dragItemIndex.current = index)}
                                onDragEnter={() => (dragOverItemIndex.current = index)}
                                onDrop={handleDrop}
                                onDragOver={(e) => e.preventDefault()}
                                sortDirection={sortColumn === colKey ? sortDirection : false}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {config.columns.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </AdminComponents.DraggableHeaderCell>
                        ))}
                        <AdminComponents.ActionTableCell align="center">Actions</AdminComponents.ActionTableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {renderGroupedRows(data, groupByKeys)}
                </TableBody>
            </AdminComponents.ResponsiveTable>
        </AdminComponents.TableViewContainer>
    );
};

// --- GENERIC DIALOG TAB VIEW ---
const DialogTabView = ({ data, columns, entityName, onAdd, config, onView, onEdit, onDelete }) => {
    const [viewMode, setViewMode] = useState('grid');
    const [selectedIds, setSelectedIds] = useState([]);

    const handleSelectAll = (event) => {
        if (event.target.checked) {
            setSelectedIds(data.map(item => item.id));
        } else {
            setSelectedIds([]);
        }
    };

    const handleSelectOne = (id) => {
        setSelectedIds(prev =>
            prev.includes(id) ? prev.filter(itemId => itemId !== id) : [...prev, id]
        );
    };

    const isItemSelected = (id) => selectedIds.indexOf(id) !== -1;

    const renderCurrentView = () => {
        switch (viewMode) {
            case 'cards':
                return (
                    <AdminComponents.GridView>
                        {data.map(item => <GenericItemCard key={item.id} item={item} columns={columns} isSelected={isItemSelected(item.id)} onSelect={handleSelectOne} config={config} onView={onView} onEdit={onEdit} onDelete={onDelete} />)}
                    </AdminComponents.GridView>
                );
            case 'compact':
                return (
                    <AdminComponents.CompactView>
                        {data.map(item => <GenericCompactCard key={item.id} item={item} columns={columns} isSelected={isItemSelected(item.id)} onSelect={handleSelectOne} config={config} onView={onView} onEdit={onEdit} onDelete={onDelete} />)}
                    </AdminComponents.CompactView>
                );
            case 'list':
                return (
                    <AdminComponents.ListView>
                        {data.map(item => <GenericListItem key={item.id} item={item} columns={columns} isSelected={isItemSelected(item.id)} onSelect={handleSelectOne} config={config} onView={onView} onEdit={onEdit} onDelete={onDelete} />)}
                    </AdminComponents.ListView>
                );
            case 'grid':
            default:
                return (
                    <TableContainer component={Paper}>
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < data.length} checked={data.length > 0 && selectedIds.length === data.length} onChange={handleSelectAll} /></TableCell>
                                    {columns.map(col => <TableCell key={col.key}>{col.label}</TableCell>)}
                                    <TableCell align="center">Actions</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {data.map(item => {
                                    const isSelected = isItemSelected(item.id);
                                    return (
                                        <TableRow key={item.id} hover role="checkbox" aria-checked={isSelected} tabIndex={-1} selected={isSelected}>
                                            <TableCell padding="checkbox"><Checkbox checked={isSelected} onChange={() => handleSelectOne(item.id)} /></TableCell>
                                            {columns.map(col => (
                                                <TableCell key={col.key}>
                                                    {col.key.includes('.') ?
                                                        col.key.split('.').reduce((obj, key) => obj?.[key], item) :
                                                        item[col.key]}
                                                </TableCell>
                                            ))}
                                            <TableCell align="center">
                                                <DialogActionButtons onView={onView} onEdit={onEdit} onDelete={onDelete} item={item} config={config} />
                                            </TableCell>
                                        </TableRow>
                                    );
                                })}
                            </TableBody>
                        </Table>
                    </TableContainer>
                );
        }
    };

    return (
        <Box>
            <AdminComponents.DialogTabViewControls>
                <AdminComponents.StyledToggleButtonGroup
                    size="small"
                    value={viewMode}
                    exclusive
                    onChange={(e, v) => v && setViewMode(v)}
                >
                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                </AdminComponents.StyledToggleButtonGroup>
                <AdminComponents.DialogTabViewActions>
                    <Button variant="contained" startIcon={<Add />} onClick={onAdd}>Add {entityName}</Button>
                </AdminComponents.DialogTabViewActions>
            </AdminComponents.DialogTabViewControls>
            {renderCurrentView()}
        </Box>
    );
};

// --- GENERIC CHART COMPONENT ---
const GenericChart = ({ item, chartType, config }) => {
    const chartRef = useRef(null);
    const chartInstance = useRef(null);

    useEffect(() => {
        if (!chartRef.current || !item || !config.chartData) return;

        // Destroy existing chart
        if (chartInstance.current) {
            chartInstance.current.destroy();
        }

        const ctx = chartRef.current.getContext('2d');
        const chartData = config.chartData(item);

        chartInstance.current = new Chart(ctx, {
            type: chartType,
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    title: {
                        display: true,
                        text: config.chartTitle || `${config.entityName} Analytics`
                    }
                },
                ...(chartType === 'line' && {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                })
            }
        });

        return () => {
            if (chartInstance.current) {
                chartInstance.current.destroy();
            }
        };
    }, [item, chartType, config]);

    if (!item) {
        return (
            <AdminComponents.CenteredMessage>
                <Typography variant="h6" color="text.secondary">
                    Select a {config.entityName?.toLowerCase()} to view analytics
                </Typography>
            </AdminComponents.CenteredMessage>
        );
    }

    return (
        <AdminComponents.ChartContainer>
            <canvas ref={chartRef} />
        </AdminComponents.ChartContainer>
    );
};

// --- GENERIC FORM FIELD RENDERER ---
const renderFormField = (field, formData, handleChange, isViewOnly, dropdownData) => {
    const value = field.key.includes('.') ?
        field.key.split('.').reduce((obj, key) => obj?.[key], formData) || '' :
        formData[field.key] || '';

    switch (field.type) {
        case 'text':
        case 'email':
        case 'phone':
        case 'number':
            return (
                <TextField
                    label={field.label}
                    name={field.key}
                    type={field.type === 'number' ? 'number' : 'text'}
                    value={value}
                    onChange={handleChange}
                    fullWidth
                    disabled={isViewOnly}
                    variant="outlined"
                    required={field.required}
                    multiline={field.multiline}
                    rows={field.rows}
                    InputProps={field.endAdornment ? { endAdornment: field.endAdornment } : undefined}
                />
            );

        case 'select':
            return (
                <FormControl fullWidth disabled={isViewOnly} variant="outlined">
                    <InputLabel>{field.label}</InputLabel>
                    <Select
                        native
                        label={field.label}
                        value={value}
                        name={field.key}
                        onChange={handleChange}
                    >
                        <option aria-label="None" value="" />
                        {(dropdownData[field.optionsKey] || field.options || []).map(opt => (
                            <option key={opt.value} value={opt.value}>{opt.label}</option>
                        ))}
                    </Select>
                </FormControl>
            );

        case 'checkbox':
            return (
                <FormControlLabel
                    control={
                        <Checkbox
                            checked={!!value}
                            onChange={handleChange}
                            name={field.key}
                            disabled={isViewOnly}
                        />
                    }
                    label={field.label}
                />
            );

        default:
            return (
                <TextField
                    label={field.label}
                    name={field.key}
                    value={value}
                    onChange={handleChange}
                    fullWidth
                    disabled={isViewOnly}
                    variant="outlined"
                    required={field.required}
                />
            );
    }
};

// --- GENERIC DIALOG COMPONENT ---
const GenericDialog = ({ open, onClose, itemData, mode, onSave, config, dropdownData = {} }) => {
    const [formData, setFormData] = useState({});
    const [tabValue, setTabValue] = useState(0);

    useEffect(() => {
        setFormData(itemData || {});
        setTabValue(0); // Reset to first tab when dialog opens/re-opens
    }, [itemData, open]);

    const isViewOnly = mode === 'view';
    const title = mode === 'add' ? `Add ${config.entityName}` :
                  mode === 'edit' ? `Edit ${config.entityName}` :
                  `View ${config.entityName}`;

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;

        if (name.includes('.')) {
            // Handle nested object properties
            const keys = name.split('.');
            setFormData(prev => {
                const newData = { ...prev };
                let current = newData;
                for (let i = 0; i < keys.length - 1; i++) {
                    if (!current[keys[i]]) current[keys[i]] = {};
                    current = current[keys[i]];
                }
                current[keys[keys.length - 1]] = type === 'checkbox' ? checked : value;
                return newData;
            });
        } else {
            setFormData(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
        }
    };

    const handleSave = () => {
        onSave({ ...formData, isDraft: false });
        onClose();
    };

    const handleSaveDraft = () => {
        onSave({ ...formData, isDraft: true });
        onClose();
    };

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    const handleNextTab = () => {
        setTabValue(prev => Math.min(prev + 1, (config.formTabs?.length || 1) - 1));
    };

    const handlePreviousTab = () => {
        setTabValue(prev => Math.max(prev - 1, 0));
    };

    return (
        <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
            <AdminComponents.DialogHeaderContainer>
                <AdminComponents.DialogHeader>
                    <AdminComponents.DialogProgressContainer>
                        <Typography variant="h6">{title}</Typography>
                    </AdminComponents.DialogProgressContainer>
                    <Box>
                        {!isViewOnly && <Button color="inherit" startIcon={<Save />} onClick={handleSave}>Save</Button>}
                        <Button color="inherit" startIcon={<Close />} onClick={onClose}>Exit</Button>
                    </Box>
                </AdminComponents.DialogHeader>

                {config.formTabs && config.formTabs.length > 1 && (
                    <>
                        <AdminComponents.StepperBox>
                            <Stepper alternativeLabel activeStep={tabValue} connector={<AdminComponents.CustomStepConnector />}>
                                {config.formTabs.map((tab, index) => (
                                    <Step key={tab.label} completed={tabValue > index}>
                                        <StepLabel StepIconComponent={StepIcon}>{""}</StepLabel>
                                    </Step>
                                ))}
                            </Stepper>
                        </AdminComponents.StepperBox>
                        <AdminComponents.TabsContainer>
                            <AdminComponents.StyledTabs
                                value={tabValue}
                                onChange={handleTabChange}
                                variant="fullWidth"
                                aria-label={`${config.entityName} details tabs`}
                            >
                                {config.formTabs.map((tab) => (
                                    <AdminComponents.StyledTab key={tab.label} label={tab.label} />
                                ))}
                            </AdminComponents.StyledTabs>
                        </AdminComponents.TabsContainer>
                    </>
                )}
            </AdminComponents.DialogHeaderContainer>

            <AdminComponents.ScrollableDialogContent>
                {config.formTabs ? (
                    config.formTabs.map((tab, index) => (
                        <TabPanel key={tab.label} value={tabValue} index={index}>
                            {tab.type === 'form' ? (
                                tab.sections.map((section, sectionIndex) => (
                                    <AdminComponents.FormSection key={sectionIndex}>
                                        <AdminComponents.FormSectionTitle>{section.title}</AdminComponents.FormSectionTitle>
                                        <Grid container spacing={2}>
                                            {section.fields.map((field) => (
                                                <Grid item xs={field.xs || 12} sm={field.sm} md={field.md} key={field.key}>
                                                    {renderFormField(field, formData, handleChange, isViewOnly, dropdownData)}
                                                </Grid>
                                            ))}
                                        </Grid>
                                    </AdminComponents.FormSection>
                                ))
                            ) : tab.type === 'tabView' ? (
                                <DialogTabView
                                    data={tab.data || []}
                                    columns={tab.columns || []}
                                    entityName={tab.entityName || 'Item'}
                                    onAdd={tab.onAdd}
                                    config={tab.config || config}
                                />
                            ) : null}
                        </TabPanel>
                    ))
                ) : (
                    // Single form without tabs
                    config.formSections?.map((section, sectionIndex) => (
                        <AdminComponents.FormSection key={sectionIndex}>
                            <AdminComponents.FormSectionTitle>{section.title}</AdminComponents.FormSectionTitle>
                            <Grid container spacing={2}>
                                {section.fields.map((field) => (
                                    <Grid item xs={field.xs || 12} sm={field.sm} md={field.md} key={field.key}>
                                        {renderFormField(field, formData, handleChange, isViewOnly, dropdownData)}
                                    </Grid>
                                ))}
                            </Grid>
                        </AdminComponents.FormSection>
                    ))
                )}
            </AdminComponents.ScrollableDialogContent>

            <AdminComponents.DialogFooter>
                {config.formTabs && config.formTabs.length > 1 && (
                    <AdminComponents.FooterButtonContainer>
                        <Button variant="outlined" onClick={handlePreviousTab} disabled={tabValue === 0} startIcon={<NavigateBefore />}>
                            Previous
                        </Button>
                        <AdminComponents.SpacedButton variant="outlined" onClick={handleNextTab} disabled={tabValue === config.formTabs.length - 1} endIcon={<NavigateNext />}>
                            Next
                        </AdminComponents.SpacedButton>
                    </AdminComponents.FooterButtonContainer>
                )}
                <AdminComponents.FooterSpacer />
                <AdminComponents.FooterButtonContainer>
                    {!isViewOnly && config.allowDrafts && <Button variant="outlined" onClick={handleSaveDraft}>Save as Draft</Button>}
                    {!isViewOnly && <AdminComponents.SpacedButton variant="contained" startIcon={<Save />} onClick={handleSave}>Save & Close</AdminComponents.SpacedButton>}
                    <AdminComponents.SpacedButton variant="outlined" startIcon={<Close />} onClick={onClose}>Cancel</AdminComponents.SpacedButton>
                </AdminComponents.FooterButtonContainer>
            </AdminComponents.DialogFooter>
        </Dialog>
    );
};

// --- ACTIVITY LOG COMPONENT ---
const ActivityLog = ({ logs, config }) => (
    <AdminComponents.ActivityLogContainer>
        <AdminComponents.ActivityLogHeader>
            <Typography variant="h6">{config?.activityLogTitle || 'Activity Log'}</Typography>
        </AdminComponents.ActivityLogHeader>
        <AdminComponents.ActivityLogContent>
            {logs.map((log, index) => (
                <AdminComponents.ActivityLogItem key={index}>
                    <Typography variant="body2">
                        <strong>{log.user}</strong> {log.action} <em>{log.target}</em>
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                        {log.timestamp}
                    </Typography>
                </AdminComponents.ActivityLogItem>
            ))}
        </AdminComponents.ActivityLogContent>
    </AdminComponents.ActivityLogContainer>
);

// --- MAIN VIEW DATA TEMPLATE COMPONENT ---
const ViewDataTemplate = ({ config }) => {
    // State management
    const [data, setData] = useState(config.initialData || []);
    const [selectedItem, setSelectedItem] = useState(null);
    const [modalState, setModalState] = useState({ isOpen: false, item: null, mode: 'view' });
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [activityLog, setActivityLog] = useState(config.initialActivityLog || []);

    // View and filter state
    const [viewMode, setViewMode] = useState('grid');
    const [searchTerm, setSearchTerm] = useState('');
    const [activeFilters, setActiveFilters] = useState([]);
    const [stagedFilters, setStagedFilters] = useState([]);
    const [summaryFilter, setSummaryFilter] = useState(null);
    const [sortColumn, setSortColumn] = useState(config.defaultSortColumn || config.columns[0]?.key);
    const [sortDirection, setSortDirection] = useState('asc');
    const [selectedIds, setSelectedIds] = useState([]);
    const [columnOrder, setColumnOrder] = useState(config.columns.map(col => col.key));
    const [groupByKeys, setGroupByKeys] = useState([]);

    // Chart and graph state
    const [isGraphVisible, setIsGraphVisible] = useState(false);
    const [chartType, setChartType] = useState('bar');

    // Computed values
    const displayItem = selectedItem || (selectedIds.length === 1 ? data.find(item => item.id === selectedIds[0]) : null);

    // Data processing
    const processedData = useMemo(() => {
        let filtered = data;

        // Apply search filter
        if (searchTerm) {
            filtered = filtered.filter(item =>
                config.searchFields.some(field => {
                    const value = field.includes('.') ?
                        field.split('.').reduce((obj, key) => obj?.[key], item) :
                        item[field];
                    return value?.toString().toLowerCase().includes(searchTerm.toLowerCase());
                })
            );
        }

        // Apply summary filter
        if (summaryFilter && config.statusField) {
            filtered = filtered.filter(item => {
                const status = config.statusField.includes('.') ?
                    config.statusField.split('.').reduce((obj, key) => obj?.[key], item) :
                    item[config.statusField];
                return status === summaryFilter;
            });
        }

        // Apply advanced filters
        filtered = filtered.filter(item => {
            return activeFilters.every(filter => {
                const value = filter.field.includes('.') ?
                    filter.field.split('.').reduce((obj, key) => obj?.[key], item) :
                    item[filter.field];
                const itemValue = value?.toString().toLowerCase() || '';
                const filterValue = filter.value.toLowerCase();

                switch (filter.operator) {
                    case 'Equals': return itemValue === filterValue;
                    case 'Not Equals': return itemValue !== filterValue;
                    case 'Contains': return itemValue.includes(filterValue);
                    case 'Starts With': return itemValue.startsWith(filterValue);
                    case 'Ends With': return itemValue.endsWith(filterValue);
                    default: return true;
                }
            });
        });

        // Apply sorting
        if (sortColumn) {
            filtered.sort((a, b) => {
                const aValue = sortColumn.includes('.') ?
                    sortColumn.split('.').reduce((obj, key) => obj?.[key], a) :
                    a[sortColumn];
                const bValue = sortColumn.includes('.') ?
                    sortColumn.split('.').reduce((obj, key) => obj?.[key], b) :
                    b[sortColumn];

                if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
                if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
                return 0;
            });
        }

        return filtered;
    }, [data, searchTerm, summaryFilter, activeFilters, sortColumn, sortDirection, config]);

    // Summary statistics
    const summaryStats = useMemo(() => {
        if (!config.statusField) return { total: data.length };

        const stats = { total: data.length };
        const statusCounts = data.reduce((acc, item) => {
            const status = config.statusField.includes('.') ?
                config.statusField.split('.').reduce((obj, key) => obj?.[key], item) :
                item[config.statusField];
            acc[status] = (acc[status] || 0) + 1;
            return acc;
        }, {});

        return { ...stats, ...statusCounts };
    }, [data, config.statusField]);

    // Event handlers
    const addLog = (message) => {
        const newLog = {
            user: 'User',
            action: message,
            target: config.entityName,
            timestamp: new Date().toLocaleString()
        };
        setActivityLog(prev => [newLog, ...prev]);
    };

    const handleOpenModal = (item, mode) => {
        setModalState({ isOpen: true, item, mode });
    };

    const handleCloseModal = () => {
        setModalState({ isOpen: false, item: null, mode: 'view' });
    };

    const handleSaveItem = (itemData) => {
        if (modalState.mode === 'add') {
            const newItem = { ...itemData, id: Date.now() };
            setData(prev => [...prev, newItem]);
            addLog(`Added new ${config.entityName.toLowerCase()}: ${newItem[config.displayField || 'name']}`);
        } else if (modalState.mode === 'edit') {
            setData(prev => prev.map(item =>
                item.id === itemData.id ? itemData : item
            ));
            addLog(`Updated ${config.entityName.toLowerCase()}: ${itemData[config.displayField || 'name']}`);
        }
    };

    const handleDeleteRequest = (ids) => {
        const itemsToDelete = data.filter(item => ids.includes(item.id));
        setData(prev => prev.filter(item => !ids.includes(item.id)));
        setSelectedIds(prev => prev.filter(id => !ids.includes(id)));
        addLog(`Deleted ${itemsToDelete.length} ${config.entityName.toLowerCase()}(s)`);
    };

    const handleSelectAll = (event) => {
        if (event.target.checked) {
            setSelectedIds(processedData.map(item => item.id));
        } else {
            setSelectedIds([]);
        }
    };

    const handleSelectOne = (id) => {
        setSelectedIds(prev =>
            prev.includes(id) ? prev.filter(itemId => itemId !== id) : [...prev, id]
        );
    };

    const handleToggleSidebar = (mode) => {
        if (isSidebarOpen && sidebarMode === mode) {
            setIsSidebarOpen(false);
        } else {
            setSidebarMode(mode);
            setIsSidebarOpen(true);
        }
    };

    const handleGraphToggle = () => {
        setIsGraphVisible(prev => !prev);
        addLog(isGraphVisible ? 'Closed analytics view' : 'Opened analytics view');
    };

    const handleSummaryCardClick = (status) => {
        setSummaryFilter(prevStatus => (prevStatus === status ? null : status));
        setActiveFilters([]);
        setStagedFilters([]);
        setSearchTerm('');
    };

    const handleGroupByChange = (key) => {
        setGroupByKeys(prev =>
            prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]
        );
    };

    const handleCustomAction = (actionKey, item) => {
        const action = config.customActions?.find(a => a.key === actionKey);
        if (action?.handler) {
            action.handler(item, { setData, addLog, setModalState });
        }
    };

    // Render current view
    const renderCurrentView = () => {
        const isCondensed = isSidebarOpen || isGraphVisible;
        const viewProps = {
            onDelete: handleDeleteRequest,
            onEdit: (item) => handleOpenModal(item, 'edit'),
            onView: (item) => handleOpenModal(item, 'view'),
            onCustomAction: handleCustomAction,
        };

        const isSelected = (id) => selectedIds.indexOf(id) !== -1;

        return (
            <AdminComponents.ViewContainer>
                {processedData.length > 0 ? (
                    <>
                        {viewMode === 'cards' && (
                            <AdminComponents.GridView>
                                {processedData.map(item => (
                                    <GenericItemCard
                                        key={item.id}
                                        item={item}
                                        columns={config.columns}
                                        isSelected={displayItem?.id === item.id}
                                        onSelect={handleSelectOne}
                                        isChecked={isSelected(item.id)}
                                        config={config}
                                        {...viewProps}
                                        isCondensed={false}
                                    />
                                ))}
                            </AdminComponents.GridView>
                        )}
                        {viewMode === 'grid' && (
                            <GenericTable
                                data={processedData}
                                onRowClick={setSelectedItem}
                                onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }}
                                sortColumn={sortColumn}
                                sortDirection={sortDirection}
                                selectedId={displayItem?.id}
                                selectedIds={selectedIds}
                                onSelectAll={handleSelectAll}
                                onSelectOne={handleSelectOne}
                                columnOrder={columnOrder}
                                setColumnOrder={setColumnOrder}
                                addLog={addLog}
                                groupByKeys={groupByKeys}
                                config={config}
                                {...viewProps}
                                isCondensed={isCondensed}
                            />
                        )}
                        {viewMode === 'compact' && (
                            <AdminComponents.CompactView>
                                {processedData.map(item => (
                                    <GenericCompactCard
                                        key={item.id}
                                        item={item}
                                        columns={config.columns}
                                        isSelected={displayItem?.id === item.id}
                                        onSelect={handleSelectOne}
                                        isChecked={isSelected(item.id)}
                                        config={config}
                                        {...viewProps}
                                        isCondensed={false}
                                    />
                                ))}
                            </AdminComponents.CompactView>
                        )}
                        {viewMode === 'list' && (
                            <AdminComponents.ListView>
                                {processedData.map(item => (
                                    <GenericListItem
                                        key={item.id}
                                        item={item}
                                        columns={config.columns}
                                        isSelected={displayItem?.id === item.id}
                                        onSelect={handleSelectOne}
                                        isChecked={isSelected(item.id)}
                                        config={config}
                                        {...viewProps}
                                        isCondensed={isCondensed}
                                    />
                                ))}
                            </AdminComponents.ListView>
                        )}
                    </>
                ) : (
                    <AdminComponents.CenteredMessage component={Paper}>
                        <AdminComponents.LargeIcon color="disabled" />
                        <Typography variant="h6">No Matching {config.entityName}s</Typography>
                        <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                    </AdminComponents.CenteredMessage>
                )}
            </AdminComponents.ViewContainer>
        );
    };

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard isActive={summaryFilter === null} onClick={() => handleSummaryCardClick(null)}>
                                        <AdminComponents.SummaryAvatar variant="total">
                                            {config.summaryIcons?.total || <People />}
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryStats.total}</Typography>
                                            <Typography variant="body2">Total {config.entityName}s</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    {config.statusField && config.statusOptions?.map(status => (
                                        <AdminComponents.SummaryCard
                                            key={status.value}
                                            isActive={summaryFilter === status.value}
                                            onClick={() => handleSummaryCardClick(status.value)}
                                        >
                                            <AdminComponents.SummaryAvatar variant={status.variant || 'active'}>
                                                {status.icon || <CheckCircle />}
                                            </AdminComponents.SummaryAvatar>
                                            <Box>
                                                <Typography variant="h6">{summaryStats[status.value] || 0}</Typography>
                                                <Typography variant="body2">{status.label}</Typography>
                                            </Box>
                                        </AdminComponents.SummaryCard>
                                    ))}
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button variant="contained" startIcon={<Add />} onClick={() => handleOpenModal(null, 'add')}>
                                        Add {config.entityName}
                                    </Button>
                                    {config.allowDrafts && (
                                        <Button variant="outlined" onClick={() => config.onOpenDrafts?.()}>
                                            Drafts
                                        </Button>
                                    )}
                                    {config.chartData && (
                                        <Button variant="outlined" startIcon={<BarChart />} onClick={handleGraphToggle}>
                                            Graphs
                                        </Button>
                                    )}
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>

                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField
                                    variant="outlined"
                                    size="small"
                                    placeholder={`Search ${config.entityName}s...`}
                                    value={searchTerm}
                                    onChange={e => setSearchTerm(e.target.value)}
                                    InputProps={{ startAdornment: <Search color="disabled" /> }}
                                />
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            onChange={handleSelectAll}
                                            checked={processedData.length > 0 && selectedIds.length === processedData.length}
                                            indeterminate={selectedIds.length > 0 && selectedIds.length < processedData.length}
                                        />
                                    }
                                    label="Select All"
                                />
                                {selectedIds.length > 0 && (
                                    <Button
                                        variant="outlined"
                                        color="error"
                                        startIcon={<Delete />}
                                        onClick={() => handleDeleteRequest(selectedIds)}
                                    >
                                        Delete ({selectedIds.length})
                                    </Button>
                                )}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => handleToggleSidebar('search')}>
                                    Advanced Search
                                </Button>
                                <Button variant="outlined" startIcon={<Settings />} onClick={() => handleToggleSidebar('grid')}>
                                    Table Settings
                                </Button>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(e, v) => v && setViewMode(v)}
                                >
                                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>

                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            {config.chartData && (
                                <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                    <AdminComponents.ChartTypeSelectorContainer>
                                        <AdminComponents.StyledToggleButtonGroup
                                            value={chartType}
                                            exclusive
                                            onChange={(e, newType) => newType && setChartType(newType)}
                                            size="small"
                                            fullWidth
                                        >
                                            <ToggleButton value="bar" title="Bar Chart"><BarChart />Bar</ToggleButton>
                                            <ToggleButton value="line" title="Line Chart"><ShowChart />Line</ToggleButton>
                                            <ToggleButton value="pie" title="Pie Chart"><PieChart />Pie</ToggleButton>
                                            <ToggleButton value="doughnut" title="Doughnut Chart"><DonutLarge />Donut</ToggleButton>
                                        </AdminComponents.StyledToggleButtonGroup>
                                    </AdminComponents.ChartTypeSelectorContainer>
                                    <GenericChart item={displayItem} chartType={chartType} config={config} />
                                </AdminComponents.DetailsPane>
                            )}
                        </AdminComponents.ContentBody>

                        <ActivityLog logs={activityLog} config={config} />

                    </AdminComponents.MainContentArea>
                </AdminComponents.AppBody>

                <Drawer
                    variant="persistent"
                    anchor="right"
                    open={isSidebarOpen}
                >
                    <AdminComponents.SidebarContainer>
                        <AdminComponents.SidebarHeader>
                            <Typography variant="h6">
                                {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
                            </Typography>
                            <IconButton onClick={() => setIsSidebarOpen(false)}>
                                <Close />
                            </IconButton>
                        </AdminComponents.SidebarHeader>
                        <AdminComponents.SidebarContent>
                            {sidebarMode === 'search' ? (
                                <Box>
                                    <Typography variant="subtitle2" gutterBottom>Search Filters</Typography>
                                    {/* Advanced search filters would go here */}
                                </Box>
                            ) : (
                                <Box>
                                    <Typography variant="subtitle2" gutterBottom>Column Settings</Typography>
                                    {/* Column configuration would go here */}
                                </Box>
                            )}
                        </AdminComponents.SidebarContent>
                    </AdminComponents.SidebarContainer>
                </Drawer>

                <GenericDialog
                    open={modalState.isOpen}
                    onClose={handleCloseModal}
                    itemData={modalState.item}
                    mode={modalState.mode}
                    onSave={handleSaveItem}
                    config={config}
                    dropdownData={config.dropdownData || {}}
                />

            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default ViewDataTemplate;
