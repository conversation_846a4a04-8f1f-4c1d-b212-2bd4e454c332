import { Assessment, CheckCircle, Cancel, People } from '@mui/icons-material';

// Example configuration for Customer management using ViewDataTemplate
export const customerConfig = {
    // Basic entity information
    entityName: 'Customer',
    displayField: 'name', // Primary field to display in logs and references
    statusField: 'status', // Field used for status-based filtering and badges
    
    // Search configuration
    searchFields: ['name', 'industry', 'primaryContact.name', 'primaryContact.email'],
    defaultSortColumn: 'name',
    
    // Table columns configuration
    columns: [
        { key: 'name', label: 'Name', type: 'string', groupable: true },
        { key: 'status', label: 'Status', type: 'status', groupable: true },
        { key: 'type', label: 'Tier', type: 'string', groupable: true },
        { key: 'industry', label: 'Industry', type: 'string', groupable: true },
        { key: 'customerType', label: 'Customer Type', type: 'string', groupable: true },
        { key: 'primaryContact.name', label: 'Contact Name', type: 'string', groupable: false },
        { key: 'primaryContact.email', label: 'Contact Email', type: 'string', groupable: false },
    ],
    
    // Status options for summary cards
    statusOptions: [
        { value: 'Active', label: 'Active', variant: 'active', icon: CheckCircle },
        { value: 'Inactive', label: 'Inactive', variant: 'inactive', icon: Cancel }
    ],
    
    // Summary card icons
    summaryIcons: {
        total: People
    },
    
    // Action configuration
    actions: {
        view: true,
        edit: true,
        delete: true
    },
    
    // Custom actions (beyond standard CRUD)
    customActions: [
        {
            key: 'viewProducts',
            label: 'View Products',
            icon: Assessment,
            handler: (item, { setModalState }) => {
                // Custom handler for viewing products
                console.log('View products for:', item.name);
            }
        }
    ],
    
    // Form configuration for add/edit dialog
    formTabs: [
        {
            label: 'Customer',
            type: 'form',
            sections: [
                {
                    title: 'Primary Information',
                    fields: [
                        { key: 'name', label: 'Name *', type: 'text', required: true, xs: 12, md: 6 },
                        { key: 'shortName', label: 'Short Name *', type: 'text', required: true, xs: 12, md: 6 },
                        { key: 'address', label: 'Address *', type: 'text', required: true, multiline: true, rows: 2, xs: 12 }
                    ]
                },
                {
                    title: 'Company Details',
                    fields: [
                        { key: 'parentCompany', label: 'Parent Company', type: 'select', optionsKey: 'parentCompanies', xs: 12, sm: 6, md: 3 },
                        { key: 'companyType', label: 'Company Type *', type: 'select', optionsKey: 'companyTypes', required: true, xs: 12, sm: 6, md: 3 },
                        { key: 'industry', label: 'Industry', type: 'select', optionsKey: 'industries', xs: 12, sm: 6, md: 3 },
                        { key: 'currency', label: 'Currency *', type: 'select', optionsKey: 'currencies', required: true, xs: 12, sm: 6, md: 3 }
                    ]
                },
                {
                    title: 'Configuration',
                    fields: [
                        { key: 'orderingCost', label: 'Ordering Cost *', type: 'number', required: true, xs: 12, sm: 6, md: 3 },
                        { key: 'workOrderCushion', label: 'Work Order Cushion Hours *', type: 'number', required: true, xs: 12, sm: 6, md: 3 },
                        { key: 'inventoryFactor', label: 'Inventory Carrying Factor (%) *', type: 'number', required: true, endAdornment: '%', xs: 12, sm: 6, md: 3 },
                        { key: 'logoName', label: 'Logo Name', type: 'text', xs: 12, sm: 6, md: 3 }
                    ]
                }
            ]
        },
        {
            label: 'Branch',
            type: 'tabView',
            entityName: 'Branch',
            columns: [
                { key: 'name', label: 'Name' },
                { key: 'location', label: 'Location' },
                { key: 'phone', label: 'Phone' },
                { key: 'email', label: 'Email' },
                { key: 'isHeadOffice', label: 'Head Office?' }
            ],
            data: [], // Would be populated with branch data
            onAdd: () => console.log('Add branch')
        },
        {
            label: 'Company-Company Relation',
            type: 'tabView',
            entityName: 'Relation',
            columns: [
                { key: 'name', label: 'Partner/Company' },
                { key: 'partnerType', label: 'Partner Type' }
            ],
            data: [], // Would be populated with relation data
            onAdd: () => console.log('Add relation')
        },
        {
            label: 'Brands Association',
            type: 'tabView',
            entityName: 'Brand',
            columns: [
                { key: 'name', label: 'Name' }
            ],
            data: [], // Would be populated with brand data
            onAdd: () => console.log('Add brand')
        },
        {
            label: 'Employee',
            type: 'tabView',
            entityName: 'Employee',
            columns: [
                { key: 'name', label: 'Name' },
                { key: 'designation', label: 'Designation' },
                { key: 'department', label: 'Department' },
                { key: 'email', label: 'Email' },
                { key: 'mobile', label: 'Mobile' }
            ],
            data: [], // Would be populated with employee data
            onAdd: () => console.log('Add employee')
        }
    ],
    
    // Dropdown data for form fields
    dropdownData: {
        parentCompanies: [
            { value: 'Innovate Corp', label: 'Innovate Corp' },
            { value: 'Synergy Ltd', label: 'Synergy Ltd' }
        ],
        companyTypes: [
            { value: 'Partner', label: 'Partner' },
            { value: 'Manufacturer', label: 'Manufacturer' }
        ],
        industries: [
            { value: 'Technology', label: 'Technology' },
            { value: 'Healthcare', label: 'Healthcare' },
            { value: 'Finance', label: 'Finance' },
            { value: 'Automotive', label: 'Automotive' }
        ],
        currencies: [
            { value: 'USD', label: 'USD' },
            { value: 'EUR', label: 'EUR' },
            { value: 'GBP', label: 'GBP' }
        ]
    },
    
    // Chart configuration (optional)
    chartData: (item) => ({
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Revenue',
            data: [12, 19, 3, 5, 2, 3],
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    }),
    chartTitle: 'Customer Analytics',
    
    // Feature flags
    allowDrafts: true,
    
    // Initial data (would typically come from API)
    initialData: [
        {
            id: 1,
            name: 'Volvo',
            industry: 'Automotive',
            status: 'Active',
            type: 'Enterprise',
            customerType: 'EE',
            primaryContact: {
                name: 'Anna Svensson',
                email: '<EMAIL>',
                phone: '+46-31-66-90-00'
            }
        },
        {
            id: 2,
            name: 'Daimler',
            industry: 'Automotive',
            status: 'Active',
            type: 'Enterprise',
            customerType: 'EE',
            primaryContact: {
                name: 'Klaus Fischer',
                email: '<EMAIL>',
                phone: '+49-711-17-0'
            }
        }
    ],
    
    // Initial activity log
    initialActivityLog: [
        {
            user: 'Admin',
            action: 'Created a new view',
            target: 'Customer Satisfaction Overview',
            timestamp: '3/15/2023, 10:25:00 AM'
        }
    ]
};

// Example configuration for Employee management
export const employeeConfig = {
    entityName: 'Employee',
    displayField: 'name',
    statusField: 'status',
    searchFields: ['name', 'email', 'department', 'designation'],
    defaultSortColumn: 'name',
    
    columns: [
        { key: 'name', label: 'Name', type: 'string', groupable: true },
        { key: 'code', label: 'Employee Code', type: 'string', groupable: false },
        { key: 'email', label: 'Email', type: 'string', groupable: false },
        { key: 'department', label: 'Department', type: 'string', groupable: true },
        { key: 'designation', label: 'Designation', type: 'string', groupable: true },
        { key: 'status', label: 'Status', type: 'status', groupable: true }
    ],
    
    statusOptions: [
        { value: 'Active', label: 'Active', variant: 'active', icon: CheckCircle },
        { value: 'Inactive', label: 'Inactive', variant: 'inactive', icon: Cancel }
    ],
    
    actions: {
        view: true,
        edit: true,
        delete: true
    },
    
    // Single form without tabs
    formSections: [
        {
            title: 'Employee Information',
            fields: [
                { key: 'name', label: 'Name *', type: 'text', required: true, xs: 12, md: 6 },
                { key: 'code', label: 'Employee Code *', type: 'text', required: true, xs: 12, md: 6 },
                { key: 'email', label: 'Email *', type: 'email', required: true, xs: 12, md: 6 },
                { key: 'mobile', label: 'Mobile', type: 'phone', xs: 12, md: 6 },
                { key: 'department', label: 'Department', type: 'select', optionsKey: 'departments', xs: 12, md: 6 },
                { key: 'designation', label: 'Designation', type: 'select', optionsKey: 'designations', xs: 12, md: 6 }
            ]
        }
    ],
    
    dropdownData: {
        departments: [
            { value: 'Administration', label: 'Administration' },
            { value: 'Service', label: 'Service' },
            { value: 'Sales', label: 'Sales' }
        ],
        designations: [
            { value: 'Credit Agent', label: 'Credit Agent' },
            { value: 'DMS Administrator', label: 'DMS Administrator' },
            { value: 'Manager', label: 'Manager' }
        ]
    },
    
    initialData: [
        {
            id: 1,
            name: 'Admin',
            code: 'E001',
            mobile: '************',
            email: '<EMAIL>',
            department: 'Administration',
            designation: 'Credit Agent',
            status: 'Active'
        },
        {
            id: 2,
            name: 'Employee 11',
            code: 'A029346',
            mobile: '************',
            email: '<EMAIL>',
            department: 'Service',
            designation: 'DMS Administrator',
            status: 'Active'
        }
    ]
};
