import React, { useState, useMemo, useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, FormControlLabel, Drawer, Chip, FormControl, InputLabel, Select, MenuItem,
    Tabs, Tab, Grid, Dialog, DialogContent, DialogActions, Stepper, Step, StepLabel,
    LinearProgress, Accordion, AccordionSummary, AccordionDetails, Menu
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, MoreVert, Close, SearchOff,
    Save, NavigateBefore, NavigateNext, ExpandMore, Business, Assignment, Timeline,
    AttachMoney, Group, LocationOn, <PERSON>, <PERSON>, Date<PERSON><PERSON><PERSON>, <PERSON><PERSON>ding<PERSON><PERSON>, <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';

// --- MOCK DATA ---
const initialProjects = [
    {
        id: 1,
        // Commercial Tab
        projectCode: 'PRJ-2024-0021',
        group: 'Digital Solutions',
        ghbu: 'Global Technology Services',
        hbu: 'Application Development',
        sdu: 'Web Development',
        du: 'Frontend Team',
        projectProfile: 'Enterprise Application',
        projectCategory: 'FPP',
        signedUnsigned: 'Signed',
        verticalAccountManager: 'John Smith',
        customer: 'TechCorp Inc.',
        custSowNo: 'SOW-2024-TC-001',
        customerGeo: 'North America',
        projectName: 'E-Commerce Platform Redesign',
        projectStartDate: '2024-01-15',
        projectEndDate: '2024-06-30',
        projectManager: 'Alice Johnson',
        srProjectManager: 'Bob Wilson',
        poNumber: 'PO-2024-001',
        fmoSpocs: 'Finance Team Lead',
        projectDocuments: ['SOW.pdf', 'PO.pdf', 'MSA.pdf'],
        projectLocationPlan: 'Hybrid - Client Site & Remote',
        companyCode: 'HCL001',
        sapProjectType: 'Development',
        financialYear: '2024',
        businessArea: 'Technology Services',
        profitCentre: 'PC-001',

        // Delivery Related Tab
        projectType: 'Development',
        serviceLineProductHierarchy: 'Application Development > Web Development',
        phaseType: 'Development',
        billingFrequency: 'Monthly',
        projectNatureLevel1: 'New Development',
        projectNatureLevel2: 'Web Application',
        projectNatureLevel4: 'E-Commerce Platform',
        projectArchetype: 'Agile Development',
        primaryVertical: 'Retail',
        ecoSystem: 'AWS Cloud',
        technology: 'React, Node.js, MongoDB',
        isd: 'Integrated Service Delivery',
        tool: 'JIRA, Confluence, Git',
        methodology: 'Agile',
        domainArea: 'E-Commerce',
        domainSubArea: 'B2C Platform',
        steadyStateStartDate: '2024-04-01',
        steadyStateEndDate: '2024-06-30',
        excaliburReference: 'EXC-2024-001',
        excaliburOpportunity: 'OPP-2024-001',
        projectClassification: 'Strategic',
        nvLocation: 'US East',
        division: 'Digital Solutions',

        // Other Tab
        projectDeliveryLocation: 'New York Office',
        isManagedByHorizontal: true,
        bigDeal: true,
        backgroundVerification: true,
        utilizationBenefit: true,
        dbProfitCentre: 'DB-PC-001',
        revenueType: 'Project Revenue',
        billingType: 'Milestone',
        controllingArea: 'CA-001',
        effortValue: 2400,
        effortUnit: 'Person Hours',
        oldProjectCode: 'OLD-PRJ-001',
        linkProjectCode: 'LINK-PRJ-001',
        onsiteProjectManager: 'Carol Davis',
        onsitePmRequired: true,
        ktRequired: true,
        responsePersonForKt: 'Tech Lead',
        customerEnquiryFlag: false,
        fresherUsageFlag: true,
        pmProjectLocation: 'New York',
        secondaryVertical: 'Technology',
        horizontalAccountManager: 'David Lee',

        // Status and Progress
        status: 'Active',
        budgetingStatus: 'Approved',
        progressPercentage: 65,
        budget: 250000,
        spentAmount: 125000,
        currency: 'USD'
    },
    {
        id: 2,
        // Commercial Tab
        projectCode: 'PRJ-2024-002',
        group: 'Mobile Solutions',
        ghbu: 'Global Mobile Services',
        hbu: 'Mobile Development',
        sdu: 'iOS Development',
        du: 'Mobile Team',
        projectProfile: 'Mobile Application',
        projectCategory: 'T&M',
        signedUnsigned: 'Signed',
        verticalAccountManager: 'Sarah Connor',
        customer: 'StartupXYZ',
        custSowNo: 'SOW-2024-SXY-002',
        customerGeo: 'Europe',
        projectName: 'Mobile Banking App',
        projectStartDate: '2024-02-01',
        projectEndDate: '2024-08-15',
        projectManager: 'Mike Johnson',
        srProjectManager: 'Lisa Anderson',
        poNumber: 'PO-2024-002',
        fmoSpocs: 'Finance Manager',
        projectDocuments: ['SOW.pdf', 'PO.pdf'],
        projectLocationPlan: 'Remote',
        companyCode: 'HCL002',
        sapProjectType: 'Mobile Development',
        financialYear: '2024',
        businessArea: 'Mobile Services',
        profitCentre: 'PC-002',

        // Delivery Related Tab
        projectType: 'Mobile Development',
        serviceLineProductHierarchy: 'Mobile Development > iOS Development',
        phaseType: 'Development',
        billingFrequency: 'Bi-weekly',
        projectNatureLevel1: 'New Development',
        projectNatureLevel2: 'Mobile Application',
        projectNatureLevel4: 'Banking App',
        projectArchetype: 'Mobile First',
        primaryVertical: 'Banking',
        ecoSystem: 'iOS Ecosystem',
        technology: 'Swift, Core Data, REST APIs',
        isd: 'Mobile ISD',
        tool: 'Xcode, TestFlight, Git',
        methodology: 'Agile',
        domainArea: 'Financial Services',
        domainSubArea: 'Mobile Banking',
        steadyStateStartDate: '2024-05-01',
        steadyStateEndDate: '2024-08-15',
        excaliburReference: 'EXC-2024-002',
        excaliburOpportunity: 'OPP-2024-002',
        projectClassification: 'Standard',
        nvLocation: 'EU West',
        division: 'Mobile Solutions',

        // Other Tab
        projectDeliveryLocation: 'Remote',
        isManagedByHorizontal: false,
        bigDeal: false,
        backgroundVerification: true,
        utilizationBenefit: false,
        dbProfitCentre: 'DB-PC-002',
        revenueType: 'Service Revenue',
        billingType: 'Time & Material',
        controllingArea: 'CA-002',
        effortValue: 1800,
        effortUnit: 'Person Hours',
        oldProjectCode: '',
        linkProjectCode: '',
        onsiteProjectManager: '',
        onsitePmRequired: false,
        ktRequired: false,
        responsePersonForKt: '',
        customerEnquiryFlag: true,
        fresherUsageFlag: false,
        pmProjectLocation: 'Remote',
        secondaryVertical: 'FinTech',
        horizontalAccountManager: 'Emma Wilson',

        // Status and Progress
        status: 'Active',
        budgetingStatus: 'In Review',
        progressPercentage: 45,
        budget: 180000,
        spentAmount: 75000,
        currency: 'EUR'
    }
];

// --- COLUMN DEFINITIONS ---
const ALL_COLUMNS = [
    { key: 'projectCode', label: 'Project Code', sortable: true, groupable: true },
    { key: 'projectName', label: 'Project Name', sortable: true, groupable: true },
    { key: 'customer', label: 'Customer', sortable: true, groupable: true },
    { key: 'projectCategory', label: 'Project Category', sortable: true, groupable: true },
    { key: 'status', label: 'Status', sortable: true, groupable: true },
    { key: 'budgetingStatus', label: 'Budgeting Status', sortable: true, groupable: true },
    { key: 'projectManager', label: 'Project Manager', sortable: true, groupable: true },
    { key: 'projectType', label: 'Project Type', sortable: true, groupable: true },
    { key: 'primaryVertical', label: 'Primary Vertical', sortable: true, groupable: true },
    { key: 'projectStartDate', label: 'Start Date', sortable: true, groupable: false },
    { key: 'projectEndDate', label: 'End Date', sortable: true, groupable: false },
    { key: 'budget', label: 'Budget', sortable: true, groupable: false },
    { key: 'spentAmount', label: 'Spent Amount', sortable: true, groupable: false },
    { key: 'progressPercentage', label: 'Progress %', sortable: true, groupable: false },
    { key: 'signedUnsigned', label: 'Contract Status', sortable: true, groupable: true },
    { key: 'projectDeliveryLocation', label: 'Delivery Location', sortable: true, groupable: true }
];

// --- DROPDOWN OPTIONS ---
const PROJECT_CATEGORIES = ['FPP', 'T&M', 'Fixed Price', 'Hybrid'];
const PROJECT_STATUSES = ['Active', 'Completed', 'On Hold', 'Cancelled', 'Planning'];
const BUDGETING_STATUSES = ['Approved', 'In Review', 'Pending', 'Rejected'];
const CONTRACT_STATUSES = ['Signed', 'Unsigned', 'In Progress'];
const PROJECT_TYPES = ['Development', 'Mobile Development', 'Infrastructure', 'Research', 'Maintenance', 'Consulting'];
const CURRENCIES = ['USD', 'EUR', 'GBP', 'INR'];
const BILLING_FREQUENCIES = ['Monthly', 'Bi-weekly', 'Quarterly', 'Milestone-based'];
const METHODOLOGIES = ['Agile', 'Waterfall', 'Hybrid', 'DevOps'];
const PRIMARY_VERTICALS = ['Banking', 'Retail', 'Healthcare', 'Manufacturing', 'Technology', 'Government'];
const BUSINESS_GROUPS = ['Digital Solutions', 'Mobile Solutions', 'Infrastructure Services', 'Consulting Services'];
const PHASE_TYPES = ['Planning', 'Development', 'Testing', 'Deployment', 'Maintenance'];
const EFFORT_UNITS = ['Person Hours', 'Person Days', 'Person Months'];

// --- PROJECT DIALOG STEPS ---
const PROJECT_STEPS = [
    'Commercial',
    'Delivery Related',
    'Other'
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

// --- UTILITY FUNCTIONS ---
const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const getStatusColor = (status) => {
    switch (status) {
        case 'Active': return 'success';
        case 'Completed': return 'primary';
        case 'On Hold': return 'warning';
        case 'Cancelled': return 'error';
        default: return 'default';
    }
};

const getPriorityColor = (priority) => {
    switch (priority) {
        case 'High': return 'error';
        case 'Medium': return 'warning';
        case 'Low': return 'success';
        default: return 'default';
    }
};

// --- TAB PANEL COMPONENT ---
const TabPanel = ({ children, value, index, ...other }) => (
    <div role="tabpanel" hidden={value !== index} id={`project-tabpanel-${index}`} aria-labelledby={`project-tab-${index}`} {...other}>
        {value === index && <AdminComponents.TabPanelContainer>{children}</AdminComponents.TabPanelContainer>}
    </div>
);

// --- CONFIRMATION DIALOG ---
const ConfirmationDialog = ({ open, onClose, onConfirm, title, children }) => (
    <Dialog open={open} onClose={onClose} aria-labelledby="confirmation-dialog-title" maxWidth="xs" fullWidth>
        <AdminComponents.DialogHeader>
            <Typography variant="h6" id="confirmation-dialog-title">{title}</Typography>
        </AdminComponents.DialogHeader>
        <AdminComponents.DialogContent sx={{ p: 4 }}>
            <Typography>{children}</Typography>
        </AdminComponents.DialogContent>
        <AdminComponents.DialogActions>
            <Button onClick={onClose}>Cancel</Button>
            <Button onClick={onConfirm} color="error" variant="contained">Delete</Button>
        </AdminComponents.DialogActions>
    </Dialog>
);

// --- ACTION BUTTONS ---
const ActionButtons = ({ project, onView, onEdit, onDelete, isCondensed }) => {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const open = Boolean(anchorEl);

    const handleClick = (event) => {
        event.stopPropagation();
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    if (isCondensed) {
        return (
            <Box onClick={e => e.stopPropagation()}>
                <IconButton
                    aria-label="more"
                    id="long-button"
                    aria-controls={open ? 'long-menu' : undefined}
                    aria-expanded={open ? 'true' : undefined}
                    aria-haspopup="true"
                    onClick={handleClick}
                    size="small"
                >
                    <MoreVert />
                </IconButton>
                <Menu
                    id="long-menu"
                    MenuListProps={{
                        'aria-labelledby': 'long-button',
                    }}
                    anchorEl={anchorEl}
                    open={open}
                    onClose={handleClose}
                >
                    <MenuItem onClick={() => { onView(project); handleClose(); }}>
                        <Visibility fontSize="small" sx={{ mr: 1 }} />
                        View Details
                    </MenuItem>
                    <MenuItem onClick={() => { onEdit(project); handleClose(); }}>
                        <Edit fontSize="small" sx={{ mr: 1 }} />
                        Edit
                    </MenuItem>
                    <MenuItem onClick={() => { onDelete([project.id]); handleClose(); }}>
                        <Delete fontSize="small" sx={{ mr: 1 }} />
                        Delete
                    </MenuItem>
                </Menu>
            </Box>
        );
    }

    return (
        <Box onClick={e => e.stopPropagation()}>
            <IconButton size="small" onClick={() => onView(project)} title="View Details">
                <Visibility fontSize="small" />
            </IconButton>
            <IconButton size="small" onClick={() => onEdit(project)} title="Edit">
                <Edit fontSize="small" />
            </IconButton>
            <IconButton size="small" color="error" onClick={() => onDelete([project.id])} title="Delete">
                <Delete fontSize="small" />
            </IconButton>
        </Box>
    );
};

// --- PROJECT CARD COMPONENT ---
const ProjectCard = ({ project, isSelected, onSelect, isChecked, onDelete, onEdit, onView, isCondensed }) => {
    const remainingBudget = project.budget - project.spentAmount;

    return (
        <AdminComponents.CardBase isSelected={isSelected}>
            <Checkbox
                className="card-checkbox"
                checked={isChecked}
                onChange={() => onSelect(project.id)}
                onClick={e => e.stopPropagation()}
            />
            <AdminComponents.CardActionContainer>
                <ActionButtons {...{ project, onView, onEdit, onDelete, isCondensed }} />
            </AdminComponents.CardActionContainer>
            <AdminComponents.PaddedCardContent>
                <Box display="flex" alignItems="center" gap={1} mb={1}>
                    <Assignment color="primary" />
                    <Typography variant="h6" component="div" noWrap>{project.projectName}</Typography>
                </Box>
                <Typography color="text.secondary" variant="body2" noWrap gutterBottom>
                    {project.projectCode} • {project.customer}
                </Typography>
                <Box display="flex" gap={1} mb={2}>
                    <Chip
                        label={project.status}
                        color={getStatusColor(project.status)}
                        size="small"
                    />
                    <Chip
                        label={project.projectCategory}
                        color="primary"
                        size="small"
                    />
                    <Chip
                        label={project.budgetingStatus}
                        color={project.budgetingStatus === 'Approved' ? 'success' : 'warning'}
                        size="small"
                    />
                </Box>
                <AdminComponents.CardDivider />
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                        <Group fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                        {project.projectManager}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                        <Business fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                        {project.primaryVertical}
                    </Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                        <LocationOn fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                        {project.projectDeliveryLocation}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                        {project.signedUnsigned}
                    </Typography>
                </Box>
                <Box mb={1}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
                        <Typography variant="body2">Progress</Typography>
                        <Typography variant="body2" fontWeight="bold">{project.progressPercentage}%</Typography>
                    </Box>
                    <LinearProgress
                        variant="determinate"
                        value={project.progressPercentage}
                        sx={{ height: 6, borderRadius: 3 }}
                    />
                </Box>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="body2" color="text.secondary">
                        <AttachMoney fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                        {formatCurrency(project.spentAmount, project.currency)} / {formatCurrency(project.budget, project.currency)}
                    </Typography>
                    <Typography variant="body2" color={remainingBudget >= 0 ? 'success.main' : 'error.main'}>
                        {formatCurrency(remainingBudget, project.currency)} left
                    </Typography>
                </Box>
            </AdminComponents.PaddedCardContent>
        </AdminComponents.CardBase>
    );
};

// --- PROJECT COMPACT CARD COMPONENT ---
const ProjectCompactCard = ({ project, isSelected, onSelect, isChecked, onDelete, onEdit, onView, isCondensed }) => (
    <AdminComponents.CardBase isSelected={isSelected}>
        <Checkbox
            className="card-checkbox"
            checked={isChecked}
            onChange={() => onSelect(project.id)}
            onClick={e => e.stopPropagation()}
        />
        <AdminComponents.CardActionContainer>
            <ActionButtons {...{ project, onView, onEdit, onDelete, isCondensed }} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <Box display="flex" alignItems="center" gap={1}>
                <Assignment color="primary" fontSize="small" />
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{project.projectName}</Typography>
            </Box>
            <Typography variant="caption" color="text.secondary">{project.projectCode} • {project.customer}</Typography>
            <Box display="flex" gap={0.5} mt={0.5}>
                <Chip label={project.status} color={getStatusColor(project.status)} size="small" />
                <Chip label={project.projectCategory} color="primary" size="small" />
                <Chip label={`${project.progressPercentage}%`} size="small" />
            </Box>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

// --- PROJECT LIST ITEM COMPONENT ---
const ProjectListItem = ({ project, isSelected, onSelect, isChecked, onDelete, onEdit, onView, isCondensed }) => (
    <AdminComponents.ListItemCard isSelected={isSelected}>
        <AdminComponents.ListItemGrid>
            <Checkbox
                checked={isChecked}
                onChange={() => onSelect(project.id)}
                onClick={e => e.stopPropagation()}
                size="small"
            />
            <Box>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>
                    <Assignment fontSize="small" sx={{ mr: 1, verticalAlign: 'middle' }} />
                    {project.projectName}
                </Typography>
                <Typography variant="body2" color="text.secondary" noWrap>
                    {project.projectCode} • {project.customer}
                </Typography>
            </Box>
            <Typography variant="body2">{project.projectCategory}</Typography>
            <Typography variant="body2">{project.projectManager}</Typography>
            <Box display="flex" gap={0.5}>
                <Chip label={project.status} color={getStatusColor(project.status)} size="small" />
                <Chip label={project.budgetingStatus} color={project.budgetingStatus === 'Approved' ? 'success' : 'warning'} size="small" />
            </Box>
            <AdminComponents.ListItemActions>
                <ActionButtons {...{ project, onView, onEdit, onDelete, isCondensed }} />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

// --- PROJECT TABLE COMPONENT ---
const ProjectTable = ({
    projects,
    onRowClick,
    onHeaderClick,
    sortColumn,
    sortDirection,
    selectedId,
    selectedIds,
    onSelectAll,
    onSelectOne,
    columnOrder,
    onDelete,
    onEdit,
    onView
}) => {
    const renderCellContent = (project, colKey) => {
        switch (colKey) {
            case 'projectCode':
                return (
                    <Typography variant="body2" fontWeight="bold" color="primary">
                        {project.projectCode}
                    </Typography>
                );
            case 'projectName':
                return (
                    <Box display="flex" alignItems="center" gap={1}>
                        <Assignment color="primary" fontSize="small" />
                        <Typography variant="body2" fontWeight="bold">{project.projectName}</Typography>
                    </Box>
                );
            case 'status':
                return <Chip label={project.status} color={getStatusColor(project.status)} size="small" />;
            case 'budgetingStatus':
                return <Chip label={project.budgetingStatus} color={project.budgetingStatus === 'Approved' ? 'success' : 'warning'} size="small" />;
            case 'projectCategory':
                return <Chip label={project.projectCategory} color="primary" size="small" />;
            case 'signedUnsigned':
                return <Chip label={project.signedUnsigned} color={project.signedUnsigned === 'Signed' ? 'success' : 'warning'} size="small" />;
            case 'projectStartDate':
            case 'projectEndDate':
                return formatDate(project[colKey]);
            case 'budget':
            case 'spentAmount':
                return formatCurrency(project[colKey], project.currency);
            case 'progressPercentage':
                return (
                    <Box display="flex" alignItems="center" gap={1}>
                        <LinearProgress
                            variant="determinate"
                            value={project.progressPercentage}
                            sx={{ width: 60, height: 4, borderRadius: 2 }}
                        />
                        <Typography variant="body2">{project.progressPercentage}%</Typography>
                    </Box>
                );
            case 'customer':
                return (
                    <Typography variant="body2">
                        <Business fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                        {project.customer}
                    </Typography>
                );
            case 'projectDeliveryLocation':
                return (
                    <Typography variant="body2">
                        <LocationOn fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                        {project.projectDeliveryLocation}
                    </Typography>
                );
            default:
                return project[colKey] || 'N/A';
        }
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox">
                            <Checkbox
                                indeterminate={selectedIds.length > 0 && selectedIds.length < projects.length}
                                checked={projects.length > 0 && selectedIds.length === projects.length}
                                onChange={onSelectAll}
                            />
                        </TableCell>
                        {columnOrder.map(colKey => {
                            const column = ALL_COLUMNS.find(c => c.key === colKey);
                            return (
                                <TableCell key={colKey}>
                                    {column?.sortable ? (
                                        <TableSortLabel
                                            active={sortColumn === colKey}
                                            direction={sortColumn === colKey ? sortDirection : 'asc'}
                                            onClick={() => onHeaderClick(colKey)}
                                        >
                                            {column.label}
                                        </TableSortLabel>
                                    ) : (
                                        column?.label || colKey
                                    )}
                                </TableCell>
                            );
                        })}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {projects.map(project => (
                        <TableRow
                            key={project.id}
                            hover
                            selected={selectedId === project.id}
                            onClick={() => onRowClick(project)}
                            sx={{ cursor: 'pointer' }}
                        >
                            <TableCell padding="checkbox">
                                <Checkbox
                                    checked={selectedIds.includes(project.id)}
                                    onChange={() => onSelectOne(project.id)}
                                    onClick={e => e.stopPropagation()}
                                />
                            </TableCell>
                            {columnOrder.map(colKey => (
                                <AdminComponents.ContentTableCell key={colKey}>
                                    {renderCellContent(project, colKey)}
                                </AdminComponents.ContentTableCell>
                            ))}
                            <AdminComponents.ActionTableCell align="center" onClick={e => e.stopPropagation()}>
                                <ActionButtons {...{ project, onView, onEdit, onDelete, isCondensed: false }} />
                            </AdminComponents.ActionTableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

// --- STEP ICON COMPONENT ---
const StepIcon = ({ icon, completed, active }) => (
    <AdminComponents.StepIconRoot ownerState={{ completed, active }}>
        {completed ? <CheckCircle sx={{ color: 'var(--success-main)', fontSize: '1.2rem' }} /> : <AdminComponents.StepIconText>{icon}</AdminComponents.StepIconText>}
    </AdminComponents.StepIconRoot>
);

// --- PROJECT DIALOG COMPONENT ---
const ProjectDialog = ({ open, onClose, projectData, mode, onSave }) => {
    const [formData, setFormData] = useState({
        projectName: '',
        description: '',
        projectType: 'Development',
        startDate: '',
        endDate: '',
        status: 'Active',
        budget: 0,
        allocatedBudget: 0,
        spentAmount: 0,
        currency: 'USD',
        projectManager: '',
        teamMembers: [],
        clientCustomer: '',
        department: 'IT Development',
        keyMilestones: [],
        progressPercentage: 0,
        priority: 'Medium',
        location: '',
        notes: ''
    });
    const [tabValue, setTabValue] = useState(0);
    const [errors, setErrors] = useState({});

    const isViewOnly = mode === 'view';
    const title = mode === 'add' ? 'Add Project' : (mode === 'edit' ? 'Edit Project' : 'View Project');

    React.useEffect(() => {
        if (open) {
            setTabValue(0);
            setFormData(projectData || {
                // Commercial Tab
                projectCode: '',
                group: '',
                ghbu: '',
                hbu: '',
                sdu: '',
                du: '',
                projectProfile: '',
                projectCategory: 'FPP',
                signedUnsigned: 'Unsigned',
                verticalAccountManager: '',
                customer: '',
                custSowNo: '',
                customerGeo: '',
                projectName: '',
                projectStartDate: '',
                projectEndDate: '',
                projectManager: '',
                srProjectManager: '',
                poNumber: '',
                fmoSpocs: '',
                projectDocuments: [],
                projectLocationPlan: '',
                companyCode: '',
                sapProjectType: '',
                financialYear: new Date().getFullYear().toString(),
                businessArea: '',
                profitCentre: '',

                // Delivery Related Tab
                projectType: 'Development',
                serviceLineProductHierarchy: '',
                phaseType: 'Planning',
                billingFrequency: 'Monthly',
                projectNatureLevel1: '',
                projectNatureLevel2: '',
                projectNatureLevel4: '',
                projectArchetype: '',
                primaryVertical: '',
                ecoSystem: '',
                technology: '',
                isd: '',
                tool: '',
                methodology: 'Agile',
                domainArea: '',
                domainSubArea: '',
                steadyStateStartDate: '',
                steadyStateEndDate: '',
                excaliburReference: '',
                excaliburOpportunity: '',
                projectClassification: '',
                nvLocation: '',
                division: '',

                // Other Tab
                projectDeliveryLocation: '',
                isManagedByHorizontal: false,
                bigDeal: false,
                backgroundVerification: false,
                utilizationBenefit: false,
                dbProfitCentre: '',
                revenueType: '',
                billingType: '',
                controllingArea: '',
                effortValue: 0,
                effortUnit: 'Person Hours',
                oldProjectCode: '',
                linkProjectCode: '',
                onsiteProjectManager: '',
                onsitePmRequired: false,
                ktRequired: false,
                responsePersonForKt: '',
                customerEnquiryFlag: false,
                fresherUsageFlag: false,
                pmProjectLocation: '',
                secondaryVertical: '',
                horizontalAccountManager: '',

                // Status and Progress
                status: 'Active',
                budgetingStatus: 'Pending',
                progressPercentage: 0,
                budget: 0,
                spentAmount: 0,
                currency: 'USD'
            });
            setErrors({});
        }
    }, [open, projectData]);

    const handleChange = (field, value) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        if (errors[field]) setErrors(prev => ({ ...prev, [field]: undefined }));
    };

    const handleSave = () => {
        const validationErrors = {};
        if (!formData.projectName) validationErrors.projectName = 'Project name is required';
        if (!formData.projectCode) validationErrors.projectCode = 'Project code is required';
        if (!formData.customer) validationErrors.customer = 'Customer is required';
        if (!formData.projectStartDate) validationErrors.projectStartDate = 'Project start date is required';
        if (!formData.projectEndDate) validationErrors.projectEndDate = 'Project end date is required';
        if (!formData.projectManager) validationErrors.projectManager = 'Project manager is required';

        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        onSave(formData);
        onClose();
    };

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    const handleNextTab = () => {
        setTabValue(prev => Math.min(prev + 1, PROJECT_STEPS.length - 1));
    };

    const handlePreviousTab = () => {
        setTabValue(prev => Math.max(prev - 1, 0));
    };

    // Commercial Tab
    const renderCommercialTab = () => (
        <AdminComponents.FormSection>
            <AdminComponents.FormSectionTitle>Commercial Information</AdminComponents.FormSectionTitle>
            <Grid container spacing={3}>
                <Grid item xs={12} md={6}><TextField label="Project Code *" name="projectCode" value={formData.projectCode} onChange={e => handleChange('projectCode', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" error={!!errors.projectCode} helperText={errors.projectCode} /></Grid>
                <Grid item xs={12} md={6}><TextField label="Group" name="group" value={formData.group} onChange={e => handleChange('group', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="GHBU" name="ghbu" value={formData.ghbu} onChange={e => handleChange('ghbu', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="HBU" name="hbu" value={formData.hbu} onChange={e => handleChange('hbu', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="SDU" name="sdu" value={formData.sdu} onChange={e => handleChange('sdu', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="DU" name="du" value={formData.du} onChange={e => handleChange('du', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Project Profile" name="projectProfile" value={formData.projectProfile} onChange={e => handleChange('projectProfile', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><FormControl fullWidth variant="outlined" disabled={isViewOnly}><InputLabel>Project Category</InputLabel><Select label="Project Category" value={formData.projectCategory} onChange={e => handleChange('projectCategory', e.target.value)}>{PROJECT_CATEGORIES.map(category => (<MenuItem key={category} value={category}>{category}</MenuItem>))}</Select></FormControl></Grid>
                <Grid item xs={12} md={6}><FormControl fullWidth variant="outlined" disabled={isViewOnly}><InputLabel>Signed/Unsigned</InputLabel><Select label="Signed/Unsigned" value={formData.signedUnsigned} onChange={e => handleChange('signedUnsigned', e.target.value)}>{CONTRACT_STATUSES.map(status => (<MenuItem key={status} value={status}>{status}</MenuItem>))}</Select></FormControl></Grid>
                <Grid item xs={12} md={6}><TextField label="Vertical Account Manager" name="verticalAccountManager" value={formData.verticalAccountManager} onChange={e => handleChange('verticalAccountManager', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Customer *" name="customer" value={formData.customer} onChange={e => handleChange('customer', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" error={!!errors.customer} helperText={errors.customer} /></Grid>
                <Grid item xs={12} md={6}><TextField label="Customer SOW NO" name="custSowNo" value={formData.custSowNo} onChange={e => handleChange('custSowNo', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Customer Geo" name="customerGeo" value={formData.customerGeo} onChange={e => handleChange('customerGeo', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Project Name *" name="projectName" value={formData.projectName} onChange={e => handleChange('projectName', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" error={!!errors.projectName} helperText={errors.projectName} /></Grid>
                <Grid item xs={12} md={6}><TextField label="Project Start Date *" name="projectStartDate" type="date" value={formData.projectStartDate} onChange={e => handleChange('projectStartDate', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" InputLabelProps={{ shrink: true }} error={!!errors.projectStartDate} helperText={errors.projectStartDate} /></Grid>
                <Grid item xs={12} md={6}><TextField label="Project End Date *" name="projectEndDate" type="date" value={formData.projectEndDate} onChange={e => handleChange('projectEndDate', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" InputLabelProps={{ shrink: true }} error={!!errors.projectEndDate} helperText={errors.projectEndDate} /></Grid>
                <Grid item xs={12} md={6}><TextField label="Project Manager *" name="projectManager" value={formData.projectManager} onChange={e => handleChange('projectManager', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" error={!!errors.projectManager} helperText={errors.projectManager} /></Grid>
                <Grid item xs={12} md={6}><TextField label="Sr. Project Manager" name="srProjectManager" value={formData.srProjectManager} onChange={e => handleChange('srProjectManager', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="PO Number" name="poNumber" value={formData.poNumber} onChange={e => handleChange('poNumber', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="FMO Spocs" name="fmoSpocs" value={formData.fmoSpocs} onChange={e => handleChange('fmoSpocs', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Project Documents" name="projectDocuments" value={formData.projectDocuments} onChange={e => handleChange('projectDocuments', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Project Location & Plan" name="projectLocationPlan" value={formData.projectLocationPlan} onChange={e => handleChange('projectLocationPlan', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Company Code" name="companyCode" value={formData.companyCode} onChange={e => handleChange('companyCode', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="SAP Project Type" name="sapProjectType" value={formData.sapProjectType} onChange={e => handleChange('sapProjectType', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Financial Year" name="financialYear" value={formData.financialYear} onChange={e => handleChange('financialYear', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Business Area" name="businessArea" value={formData.businessArea} onChange={e => handleChange('businessArea', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Profit Centre" name="profitCentre" value={formData.profitCentre} onChange={e => handleChange('profitCentre', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
            </Grid>
        </AdminComponents.FormSection>
    );

    // Delivery Related Tab
    const renderDeliveryRelatedTab = () => (
        <AdminComponents.FormSection>
            <AdminComponents.FormSectionTitle>Delivery Related Information</AdminComponents.FormSectionTitle>
            <Grid container spacing={3}>
                <Grid item xs={12} md={6}><FormControl fullWidth variant="outlined" disabled={isViewOnly}><InputLabel>Project Type</InputLabel><Select label="Project Type" value={formData.projectType} onChange={e => handleChange('projectType', e.target.value)}>{PROJECT_TYPES.map(type => (<MenuItem key={type} value={type}>{type}</MenuItem>))}</Select></FormControl></Grid>
                <Grid item xs={12} md={6}><TextField label="Service Line (Product Hierarchy)" name="serviceLineProductHierarchy" value={formData.serviceLineProductHierarchy} onChange={e => handleChange('serviceLineProductHierarchy', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><FormControl fullWidth variant="outlined" disabled={isViewOnly}><InputLabel>Phase Type</InputLabel><Select label="Phase Type" value={formData.phaseType} onChange={e => handleChange('phaseType', e.target.value)}>{PHASE_TYPES.map(phase => (<MenuItem key={phase} value={phase}>{phase}</MenuItem>))}</Select></FormControl></Grid>
                <Grid item xs={12} md={6}><FormControl fullWidth variant="outlined" disabled={isViewOnly}><InputLabel>Billing Frequency</InputLabel><Select label="Billing Frequency" value={formData.billingFrequency} onChange={e => handleChange('billingFrequency', e.target.value)}>{BILLING_FREQUENCIES.map(freq => (<MenuItem key={freq} value={freq}>{freq}</MenuItem>))}</Select></FormControl></Grid>
                <Grid item xs={12} md={6}><TextField label="Project Nature Level 1" name="projectNatureLevel1" value={formData.projectNatureLevel1} onChange={e => handleChange('projectNatureLevel1', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Project Nature Level 2" name="projectNatureLevel2" value={formData.projectNatureLevel2} onChange={e => handleChange('projectNatureLevel2', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Project Nature Level 4" name="projectNatureLevel4" value={formData.projectNatureLevel4} onChange={e => handleChange('projectNatureLevel4', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Project Archetype" name="projectArchetype" value={formData.projectArchetype} onChange={e => handleChange('projectArchetype', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><FormControl fullWidth variant="outlined" disabled={isViewOnly}><InputLabel>Primary Vertical</InputLabel><Select label="Primary Vertical" value={formData.primaryVertical} onChange={e => handleChange('primaryVertical', e.target.value)}>{PRIMARY_VERTICALS.map(vertical => (<MenuItem key={vertical} value={vertical}>{vertical}</MenuItem>))}</Select></FormControl></Grid>
                <Grid item xs={12} md={6}><TextField label="Eco-System" name="ecoSystem" value={formData.ecoSystem} onChange={e => handleChange('ecoSystem', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Technology" name="technology" value={formData.technology} onChange={e => handleChange('technology', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="ISD (Integrated Service Delivery)" name="isd" value={formData.isd} onChange={e => handleChange('isd', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Tool" name="tool" value={formData.tool} onChange={e => handleChange('tool', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><FormControl fullWidth variant="outlined" disabled={isViewOnly}><InputLabel>Methodology</InputLabel><Select label="Methodology" value={formData.methodology} onChange={e => handleChange('methodology', e.target.value)}>{METHODOLOGIES.map(method => (<MenuItem key={method} value={method}>{method}</MenuItem>))}</Select></FormControl></Grid>
                <Grid item xs={12} md={6}><TextField label="Domain Area" name="domainArea" value={formData.domainArea} onChange={e => handleChange('domainArea', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Domain Sub Area" name="domainSubArea" value={formData.domainSubArea} onChange={e => handleChange('domainSubArea', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Steady State Start Date" name="steadyStateStartDate" type="date" value={formData.steadyStateStartDate} onChange={e => handleChange('steadyStateStartDate', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" InputLabelProps={{ shrink: true }} /></Grid>
                <Grid item xs={12} md={6}><TextField label="Steady State End Date" name="steadyStateEndDate" type="date" value={formData.steadyStateEndDate} onChange={e => handleChange('steadyStateEndDate', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" InputLabelProps={{ shrink: true }} /></Grid>
                <Grid item xs={12} md={6}><TextField label="Excalibur Reference" name="excaliburReference" value={formData.excaliburReference} onChange={e => handleChange('excaliburReference', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Excalibur Opportunity" name="excaliburOpportunity" value={formData.excaliburOpportunity} onChange={e => handleChange('excaliburOpportunity', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Project Classification" name="projectClassification" value={formData.projectClassification} onChange={e => handleChange('projectClassification', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="NV Location" name="nvLocation" value={formData.nvLocation} onChange={e => handleChange('nvLocation', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Division" name="division" value={formData.division} onChange={e => handleChange('division', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
            </Grid>
        </AdminComponents.FormSection>
    );

    // Other Tab
    const renderOtherTab = () => (
        <AdminComponents.FormSection>
            <AdminComponents.FormSectionTitle>Other Details</AdminComponents.FormSectionTitle>
            <Grid container spacing={3}>
                <Grid item xs={12} md={6}><TextField label="Project Delivery Location" name="projectDeliveryLocation" value={formData.projectDeliveryLocation} onChange={e => handleChange('projectDeliveryLocation', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><FormControlLabel control={<Checkbox checked={formData.isManagedByHorizontal} onChange={e => handleChange('isManagedByHorizontal', e.target.checked)} disabled={isViewOnly} />} label="Is managed by Horizontal" /></Grid>
                <Grid item xs={12} md={6}><FormControlLabel control={<Checkbox checked={formData.bigDeal} onChange={e => handleChange('bigDeal', e.target.checked)} disabled={isViewOnly} />} label="Big Deal" /></Grid>
                <Grid item xs={12} md={6}><FormControlLabel control={<Checkbox checked={formData.backgroundVerification} onChange={e => handleChange('backgroundVerification', e.target.checked)} disabled={isViewOnly} />} label="Background Verification" /></Grid>
                <Grid item xs={12} md={6}><FormControlLabel control={<Checkbox checked={formData.utilizationBenefit} onChange={e => handleChange('utilizationBenefit', e.target.checked)} disabled={isViewOnly} />} label="Utilization Benefit" /></Grid>
                <Grid item xs={12} md={6}><TextField label="DB Profit Centre" name="dbProfitCentre" value={formData.dbProfitCentre} onChange={e => handleChange('dbProfitCentre', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Revenue Type" name="revenueType" value={formData.revenueType} onChange={e => handleChange('revenueType', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Billing Type" name="billingType" value={formData.billingType} onChange={e => handleChange('billingType', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Controlling Area" name="controllingArea" value={formData.controllingArea} onChange={e => handleChange('controllingArea', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Effort Value" name="effortValue" type="number" value={formData.effortValue} onChange={e => handleChange('effortValue', parseFloat(e.target.value) || 0)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><FormControl fullWidth variant="outlined" disabled={isViewOnly}><InputLabel>Effort Unit</InputLabel><Select label="Effort Unit" value={formData.effortUnit} onChange={e => handleChange('effortUnit', e.target.value)}>{EFFORT_UNITS.map(unit => (<MenuItem key={unit} value={unit}>{unit}</MenuItem>))}</Select></FormControl></Grid>
                <Grid item xs={12} md={6}><TextField label="Old Project Code" name="oldProjectCode" value={formData.oldProjectCode} onChange={e => handleChange('oldProjectCode', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Link Project Code" name="linkProjectCode" value={formData.linkProjectCode} onChange={e => handleChange('linkProjectCode', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Onsite Project Manager" name="onsiteProjectManager" value={formData.onsiteProjectManager} onChange={e => handleChange('onsiteProjectManager', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><FormControlLabel control={<Checkbox checked={formData.onsitePmRequired} onChange={e => handleChange('onsitePmRequired', e.target.checked)} disabled={isViewOnly} />} label="Onsite PM Required" /></Grid>
                <Grid item xs={12} md={6}><FormControlLabel control={<Checkbox checked={formData.ktRequired} onChange={e => handleChange('ktRequired', e.target.checked)} disabled={isViewOnly} />} label="KT Required" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Response Person for KT" name="responsePersonForKt" value={formData.responsePersonForKt} onChange={e => handleChange('responsePersonForKt', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><FormControlLabel control={<Checkbox checked={formData.customerEnquiryFlag} onChange={e => handleChange('customerEnquiryFlag', e.target.checked)} disabled={isViewOnly} />} label="Customer Enquiry Flag" /></Grid>
                <Grid item xs={12} md={6}><FormControlLabel control={<Checkbox checked={formData.fresherUsageFlag} onChange={e => handleChange('fresherUsageFlag', e.target.checked)} disabled={isViewOnly} />} label="Fresher Usage Flag" /></Grid>
                <Grid item xs={12} md={6}><TextField label="PM Project Location" name="pmProjectLocation" value={formData.pmProjectLocation} onChange={e => handleChange('pmProjectLocation', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Secondary Vertical" name="secondaryVertical" value={formData.secondaryVertical} onChange={e => handleChange('secondaryVertical', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                <Grid item xs={12} md={6}><TextField label="Horizontal Account Manager" name="horizontalAccountManager" value={formData.horizontalAccountManager} onChange={e => handleChange('horizontalAccountManager', e.target.value)} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
            </Grid>
        </AdminComponents.FormSection>
    );

    return (
        <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
            <AdminComponents.DialogHeaderContainer>
                <AdminComponents.DialogHeader>
                    <Typography variant="h6">{title}</Typography>
                    <Box>
                        {!isViewOnly && <Button color="inherit" onClick={handleSave} startIcon={<Save />}>Save</Button>}
                        <Button color="inherit" onClick={onClose} startIcon={<Close />}>Exit</Button>
                    </Box>
                </AdminComponents.DialogHeader>
                <AdminComponents.StepperBox>
                    <Stepper alternativeLabel activeStep={tabValue} connector={<AdminComponents.CustomStepConnector />}>
                        {PROJECT_STEPS.map((label, index) => (
                            <Step key={label} completed={tabValue > index}>
                                <StepLabel StepIconComponent={StepIcon}>{""}</StepLabel>
                            </Step>
                        ))}
                    </Stepper>
                </AdminComponents.StepperBox>
                <AdminComponents.TabsContainer>
                    <AdminComponents.StyledTabs value={tabValue} onChange={handleTabChange} variant="fullWidth">
                        {PROJECT_STEPS.map((label) => <AdminComponents.StyledTab key={label} label={label} />)}
                    </AdminComponents.StyledTabs>
                </AdminComponents.TabsContainer>
            </AdminComponents.DialogHeaderContainer>

            <AdminComponents.ScrollableDialogContent>
                <TabPanel value={tabValue} index={0}>{renderCommercialTab()}</TabPanel>
                <TabPanel value={tabValue} index={1}>{renderDeliveryRelatedTab()}</TabPanel>
                <TabPanel value={tabValue} index={2}>{renderOtherTab()}</TabPanel>
            </AdminComponents.ScrollableDialogContent>

            <AdminComponents.DialogFooter>
                <AdminComponents.FooterButtonContainer>
                    <Button variant="outlined" onClick={handlePreviousTab} disabled={tabValue === 0} startIcon={<NavigateBefore />}>
                        Previous
                    </Button>
                    <AdminComponents.SpacedButton variant="outlined" onClick={handleNextTab} disabled={tabValue === PROJECT_STEPS.length - 1} endIcon={<NavigateNext />}>
                        Next
                    </AdminComponents.SpacedButton>
                </AdminComponents.FooterButtonContainer>
                <AdminComponents.FooterSpacer />
                <AdminComponents.FooterButtonContainer>
                    {!isViewOnly && <Button variant="contained" startIcon={<Save />} onClick={handleSave}>Save & Close</Button>}
                    <AdminComponents.SpacedButton variant="outlined" startIcon={<Close />} onClick={onClose}>Cancel</AdminComponents.SpacedButton>
                </AdminComponents.FooterButtonContainer>
            </AdminComponents.DialogFooter>
        </Dialog>
    );
};

// --- PROJECT ANALYTICS GRAPH COMPONENT ---
const ProjectGraph = ({ project, chartType }) => {
    const chartRef = useRef(null);
    const chartInstance = useRef(null);

    useEffect(() => {
        if (chartInstance.current) chartInstance.current.destroy();
        if (chartRef.current && project) {
            const ctx = chartRef.current.getContext('2d');

            // Generate different chart data based on chart type
            let chartData = {};

            switch (chartType) {
                case 'bar':
                    chartData = {
                        labels: ['Planning', 'Development', 'Testing', 'Deployment', 'Maintenance'],
                        datasets: [{
                            label: 'Project Phase Progress',
                            data: [100, project.progressPercentage,
                                   project.progressPercentage > 70 ? project.progressPercentage - 20 : 0,
                                   project.progressPercentage > 90 ? project.progressPercentage - 10 : 0,
                                   project.status === 'Completed' ? 100 : 0],
                            backgroundColor: theme.palette.primary.main,
                            borderColor: theme.palette.primary.dark,
                        }]
                    };
                    break;
                case 'line':
                    chartData = {
                        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6'],
                        datasets: [{
                            label: 'Budget Utilization Over Time',
                            data: [10, 25, 45, 60, 75, (project.spentAmount / project.budget) * 100],
                            backgroundColor: 'rgba(25, 118, 210, 0.1)',
                            borderColor: theme.palette.primary.main,
                            fill: true,
                        }]
                    };
                    break;
                case 'pie':
                    chartData = {
                        labels: ['Completed', 'In Progress', 'Remaining'],
                        datasets: [{
                            label: 'Project Progress Distribution',
                            data: [
                                project.progressPercentage,
                                Math.min(20, 100 - project.progressPercentage),
                                Math.max(0, 100 - project.progressPercentage - 20)
                            ],
                            backgroundColor: [
                                theme.palette.success.main,
                                theme.palette.warning.main,
                                theme.palette.grey[300]
                            ],
                            borderColor: theme.palette.background.paper,
                        }]
                    };
                    break;
                case 'doughnut':
                    chartData = {
                        labels: ['Budget Spent', 'Budget Remaining'],
                        datasets: [{
                            label: 'Budget Analysis',
                            data: [project.spentAmount, project.budget - project.spentAmount],
                            backgroundColor: [
                                theme.palette.error.main,
                                theme.palette.success.main
                            ],
                            borderColor: theme.palette.background.paper,
                        }]
                    };
                    break;
                default:
                    chartData = {
                        labels: ['Active', 'Completed', 'On Hold'],
                        datasets: [{
                            label: 'Project Status Overview',
                            data: [65, 25, 10],
                            backgroundColor: [
                                theme.palette.success.main,
                                theme.palette.primary.main,
                                theme.palette.warning.main
                            ],
                        }]
                    };
            }

            chartInstance.current = new Chart(ctx, {
                type: chartType,
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${project.projectName} - Analytics`
                        },
                        legend: {
                            display: true,
                            position: 'bottom'
                        }
                    },
                    scales: ['bar', 'line'].includes(chartType) ? {
                        y: {
                            beginAtZero: true,
                            max: chartType === 'line' ? 100 : undefined
                        }
                    } : {}
                }
            });
        }
        return () => { if (chartInstance.current) chartInstance.current.destroy(); };
    }, [project, chartType]);

    return (
        <>
            {project ? (
                <AdminComponents.GraphCanvasContainer>
                    <canvas ref={chartRef}></canvas>
                </AdminComponents.GraphCanvasContainer>
            ) : (
                <AdminComponents.CenteredMessage>
                    <Typography>Select a project to see analytics</Typography>
                </AdminComponents.CenteredMessage>
            )}
        </>
    );
};

// --- ACTIVITY LOG COMPONENT ---
const ActivityLog = ({ logs }) => (
    <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
        <AdminComponents.ActivityLogTitle variant="h6">
            Activity Log
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <People />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <Box sx={{ flex: 1 }}>
                            <AdminComponents.ActivityLogTextContainer>
                                <Typography variant="body2" component="span" color="text.secondary">
                                    <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                    {' '}{log.action}{' '}
                                    {log.target && <AdminComponents.ActivityLogLink component="a" href="#">{log.target}</AdminComponents.ActivityLogLink>}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                    {log.timestamp}
                                </Typography>
                            </AdminComponents.ActivityLogTextContainer>
                        </Box>
                    </AdminComponents.ActivityLogListItem>
                ))}
            </Box>
        </AdminComponents.ActivityLogListContainer>
    </Box>
);

// --- MAIN PROJECTS COMPONENT ---
const Projects = () => {
    const [projects, setProjects] = useState(initialProjects);
    const [selectedProject, setSelectedProject] = useState(null);
    const [modalState, setModalState] = useState({ isOpen: false, project: null, mode: 'view' });
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [viewMode, setViewMode] = useState('cards');
    const [sortColumn, setSortColumn] = useState('projectName');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [deleteConfirmation, setDeleteConfirmation] = useState({ isOpen: false, idsToDelete: [] });
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
    const [groupByKeys, setGroupByKeys] = useState([]);
    const [isGraphVisible, setIsGraphVisible] = useState(false);
    const [summaryFilter, setSummaryFilter] = useState(null);
    const [chartType, setChartType] = useState('bar');

    // Advanced Search State
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });

    // Quick Filter Options
    const quickFilterOptions = ['Active Projects', 'High Priority', 'Over Budget', 'Behind Schedule', 'Signed Contracts'];

    // Activity Log
    const [activityLog, setActivityLog] = useState([
        { user: 'Admin', action: 'Created new project', target: 'E-Commerce Platform Redesign', timestamp: '3/15/2024, 10:25:00 AM' },
        { user: 'Project Manager', action: 'Updated project status', target: 'Mobile App Development', timestamp: '3/16/2024, 9:00:00 AM' },
        { user: 'Team Lead', action: 'Added milestone', target: 'Data Migration Project', timestamp: '3/17/2024, 1:45:00 PM' },
    ]);

    const addLog = (logEntry) => {
        const newLog = {
            ...logEntry,
            timestamp: new Date().toLocaleString('en-US', {
                month: 'numeric',
                day: 'numeric',
                year: 'numeric',
                hour: 'numeric',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            })
        };
        setActivityLog(prev => [newLog, ...prev]);
    };

    // Computed values
    const processedProjects = useMemo(() => {
        let filtered = projects;

        // Apply search filter
        if (searchTerm) {
            const searchLower = searchTerm.toLowerCase();
            filtered = filtered.filter(project =>
                project.projectName.toLowerCase().includes(searchLower) ||
                project.projectCode.toLowerCase().includes(searchLower) ||
                project.customer.toLowerCase().includes(searchLower) ||
                project.projectManager.toLowerCase().includes(searchLower) ||
                project.primaryVertical.toLowerCase().includes(searchLower) ||
                project.projectCategory.toLowerCase().includes(searchLower)
            );
        }

        // Apply advanced filters
        activeFilters.forEach(filter => {
            filtered = filtered.filter(project => {
                const value = project[filter.field];
                const filterValue = filter.value.toLowerCase();
                const projectValue = String(value).toLowerCase();

                switch (filter.operator) {
                    case 'Equals': return projectValue === filterValue;
                    case 'Not Equals': return projectValue !== filterValue;
                    case 'Contains': return projectValue.includes(filterValue);
                    case 'Starts With': return projectValue.startsWith(filterValue);
                    case 'Ends With': return projectValue.endsWith(filterValue);
                    default: return true;
                }
            });
        });

        // Apply summary filter
        if (summaryFilter) {
            filtered = filtered.filter(project => project.status === summaryFilter);
        }

        // Apply sorting
        filtered.sort((a, b) => {
            const aVal = a[sortColumn];
            const bVal = b[sortColumn];
            const modifier = sortDirection === 'asc' ? 1 : -1;

            if (typeof aVal === 'number' && typeof bVal === 'number') {
                return (aVal - bVal) * modifier;
            }
            return String(aVal).localeCompare(String(bVal)) * modifier;
        });

        return filtered;
    }, [projects, searchTerm, activeFilters, summaryFilter, sortColumn, sortDirection]);

    const summaryStats = useMemo(() => {
        const stats = {
            total: projects.length,
            active: projects.filter(p => p.status === 'Active').length,
            completed: projects.filter(p => p.status === 'Completed').length,
            onHold: projects.filter(p => p.status === 'On Hold').length,
            cancelled: projects.filter(p => p.status === 'Cancelled').length,
            totalBudget: projects.reduce((sum, p) => sum + p.budget, 0),
            totalSpent: projects.reduce((sum, p) => sum + p.spentAmount, 0),
            highPriority: projects.filter(p => p.priority === 'High').length
        };
        return stats;
    }, [projects]);

    const displayProject = selectedProject || processedProjects[0];

    // Event handlers
    const handleOpenModal = (project, mode) => {
        const projectData = project || {
            projectName: '',
            description: '',
            projectType: 'Development',
            startDate: '',
            endDate: '',
            status: 'Active',
            budget: 0,
            allocatedBudget: 0,
            spentAmount: 0,
            currency: 'USD',
            projectManager: '',
            teamMembers: [],
            clientCustomer: '',
            department: 'IT Development',
            keyMilestones: [],
            progressPercentage: 0,
            priority: 'Medium',
            location: '',
            notes: ''
        };
        setModalState({ isOpen: true, project: projectData, mode });
    };

    const handleCloseModal = () => setModalState({ isOpen: false, project: null, mode: 'view' });

    const handleSaveProject = (projectData) => {
        if (modalState.mode === 'add') {
            const newProject = { ...projectData, id: Date.now() };
            setProjects(prev => [newProject, ...prev]);
            addLog({ user: 'Admin', action: `Created new project: ${newProject.projectName}` });
        } else {
            setProjects(prev => prev.map(p => p.id === projectData.id ? { ...p, ...projectData } : p));
            addLog({ user: 'Admin', action: `Updated project: ${projectData.projectName}` });
        }
        handleCloseModal();
    };

    const handleDeleteRequest = (ids) => {
        setDeleteConfirmation({ isOpen: true, idsToDelete: Array.isArray(ids) ? ids : [ids] });
    };

    const handleConfirmDelete = () => {
        const { idsToDelete } = deleteConfirmation;
        const deletedProjects = projects.filter(p => idsToDelete.includes(p.id));
        setProjects(prev => prev.filter(p => !idsToDelete.includes(p.id)));
        setSelectedIds(prev => prev.filter(id => !idsToDelete.includes(id)));

        deletedProjects.forEach(project => {
            addLog({ user: 'Admin', action: `Deleted project: ${project.projectName}` });
        });

        setDeleteConfirmation({ isOpen: false, idsToDelete: [] });
    };

    const handleCancelDelete = () => {
        setDeleteConfirmation({ isOpen: false, idsToDelete: [] });
    };

    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedProjects.map(p => p.id) : []);
    const handleSelectOne = (id) => {
        const selectedIndex = selectedIds.indexOf(id);
        let newSelected = [];

        if (selectedIndex === -1) {
            newSelected = newSelected.concat(selectedIds, id);
        } else if (selectedIndex === 0) {
            newSelected = newSelected.concat(selectedIds.slice(1));
        } else if (selectedIndex === selectedIds.length - 1) {
            newSelected = newSelected.concat(selectedIds.slice(0, -1));
        } else if (selectedIndex > 0) {
            newSelected = newSelected.concat(
                selectedIds.slice(0, selectedIndex),
                selectedIds.slice(selectedIndex + 1),
            );
        }
        setSelectedIds(newSelected);
    };

    const handleToggleSidebar = (mode) => {
        setSidebarMode(mode);
        setIsSidebarOpen(true);
    };

    const handleGraphToggle = () => {
        setIsGraphVisible(prev => !prev);
        addLog({ user: 'Admin', action: `${!isGraphVisible ? 'Opened' : 'Closed'} project analytics` });
    };

    const handleAddQuickFilter = (filterOption) => {
        let quickFilter = {};
        switch (filterOption) {
            case 'Active Projects':
                quickFilter = { field: 'status', operator: 'Equals', value: 'Active', id: Date.now() };
                break;
            case 'High Priority':
                quickFilter = { field: 'priority', operator: 'Equals', value: 'High', id: Date.now() };
                break;
            case 'Over Budget':
                quickFilter = { field: 'budgetingStatus', operator: 'Equals', value: 'Over Budget', id: Date.now() };
                break;
            case 'Behind Schedule':
                quickFilter = { field: 'status', operator: 'Equals', value: 'Behind Schedule', id: Date.now() };
                break;
            case 'Signed Contracts':
                quickFilter = { field: 'signedUnsigned', operator: 'Equals', value: 'Signed', id: Date.now() };
                break;
            default:
                return;
        }
        setActiveFilters([...activeFilters, quickFilter]);
        addLog({ user: 'Admin', action: `Applied quick filter: ${filterOption}` });
    };

    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };

    const handleRemoveStagedFilter = (filterId) => {
        setStagedFilters(stagedFilters.filter(f => f.id !== filterId));
    };

    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
        setIsSidebarOpen(false);
        addLog({ user: 'Admin', action: `Applied ${stagedFilters.length} filter(s)` });
    };

    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
        addLog({ user: 'Admin', action: 'Reset all filters' });
    };

    const handleColumnVisibilityChange = (columnKey) => {
        const isVisible = columnOrder.includes(columnKey);
        if (isVisible && columnOrder.length > 1) {
            setColumnOrder(columnOrder.filter(key => key !== columnKey));
        } else if (!isVisible) {
            const column = ALL_COLUMNS.find(c => c.key === columnKey);
            const insertIndex = ALL_COLUMNS.findIndex(c => c.key === columnKey);
            const newOrder = [...columnOrder];
            newOrder.splice(insertIndex, 0, columnKey);
            setColumnOrder(newOrder);
        }
    };

    const handleGroupByChange = (columnKey) => {
        const isGrouped = groupByKeys.includes(columnKey);
        if (isGrouped) {
            setGroupByKeys(groupByKeys.filter(key => key !== columnKey));
        } else {
            setGroupByKeys([...groupByKeys, columnKey]);
        }
    };

    const handleSummaryCardClick = (status) => {
        setSummaryFilter(summaryFilter === status ? null : status);
        addLog({ user: 'Admin', action: `Filtered projects by status: ${status}` });
    };

    // Render current view
    const renderCurrentView = () => {
        const isCondensed = isSidebarOpen || isGraphVisible;
        const viewProps = {
            onDelete: handleDeleteRequest,
            onEdit: (project) => handleOpenModal(project, 'edit'),
            onView: (project) => handleOpenModal(project, 'view'),
        };

        const isSelected = (id) => selectedIds.indexOf(id) !== -1;

        return (
            <AdminComponents.ViewContainer>
                {processedProjects.length > 0 ? (
                    <>
                        {viewMode === 'cards' && (
                            <AdminComponents.GridView>
                                {processedProjects.map(project => (
                                    <ProjectCard
                                        key={project.id}
                                        project={project}
                                        isSelected={displayProject?.id === project.id}
                                        onSelect={handleSelectOne}
                                        isChecked={isSelected(project.id)}
                                        {...viewProps}
                                        isCondensed={isCondensed}
                                    />
                                ))}
                            </AdminComponents.GridView>
                        )}
                        {viewMode === 'grid' && (
                            <ProjectTable
                                projects={processedProjects}
                                onRowClick={setSelectedProject}
                                onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }}
                                sortColumn={sortColumn}
                                sortDirection={sortDirection}
                                selectedId={displayProject?.id}
                                selectedIds={selectedIds}
                                onSelectAll={handleSelectAll}
                                onSelectOne={handleSelectOne}
                                columnOrder={columnOrder}
                                {...viewProps}
                            />
                        )}
                        {viewMode === 'compact' && (
                            <AdminComponents.CompactView>
                                {processedProjects.map(project => (
                                    <ProjectCompactCard
                                        key={project.id}
                                        project={project}
                                        isSelected={displayProject?.id === project.id}
                                        onSelect={handleSelectOne}
                                        isChecked={isSelected(project.id)}
                                        {...viewProps}
                                        isCondensed={isCondensed}
                                    />
                                ))}
                            </AdminComponents.CompactView>
                        )}
                        {viewMode === 'list' && (
                            <AdminComponents.ListView>
                                {processedProjects.map(project => (
                                    <ProjectListItem
                                        key={project.id}
                                        project={project}
                                        isSelected={displayProject?.id === project.id}
                                        onSelect={handleSelectOne}
                                        isChecked={isSelected(project.id)}
                                        {...viewProps}
                                        isCondensed={isCondensed}
                                    />
                                ))}
                            </AdminComponents.ListView>
                        )}
                    </>
                ) : (
                    <AdminComponents.CenteredMessage component={Paper}>
                        <AdminComponents.LargeIcon color="disabled" />
                        <Typography variant="h6">No Matching Projects</Typography>
                        <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                    </AdminComponents.CenteredMessage>
                )}
            </AdminComponents.ViewContainer>
        );
    };

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard
                                        onClick={() => handleSummaryCardClick(null)}
                                        sx={{ cursor: 'pointer', opacity: summaryFilter === null ? 1 : 0.7 }}
                                    >
                                        <AdminComponents.SummaryAvatar variant="total"><Assignment /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.total}</Typography><Typography variant="body2">Total Projects</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard
                                        onClick={() => handleSummaryCardClick('Active')}
                                        sx={{ cursor: 'pointer', opacity: summaryFilter === 'Active' ? 1 : 0.7 }}
                                    >
                                        <AdminComponents.SummaryAvatar variant="active"><CheckCircle /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.active}</Typography><Typography variant="body2">Active</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard
                                        onClick={() => handleSummaryCardClick('Completed')}
                                        sx={{ cursor: 'pointer', opacity: summaryFilter === 'Completed' ? 1 : 0.7 }}
                                    >
                                        <AdminComponents.SummaryAvatar variant="completed"><Schedule /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.completed}</Typography><Typography variant="body2">Completed</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard
                                        onClick={() => handleSummaryCardClick('On Hold')}
                                        sx={{ cursor: 'pointer', opacity: summaryFilter === 'On Hold' ? 1 : 0.7 }}
                                    >
                                        <AdminComponents.SummaryAvatar variant="warning"><Cancel /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.onHold}</Typography><Typography variant="body2">On Hold</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="budget"><AttachMoney /></AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{formatCurrency(summaryStats.totalBudget)}</Typography>
                                            <Typography variant="body2">Total Budget</Typography>
                                            <Typography variant="caption" color="text.secondary">
                                                {formatCurrency(summaryStats.totalSpent)} spent
                                            </Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button variant="contained" startIcon={<Add />} onClick={() => handleOpenModal(null, 'add')}>
                                        Add Project
                                    </Button>
                                    <Button variant="outlined" startIcon={<BarChart />} onClick={handleGraphToggle}>
                                        Analytics
                                    </Button>
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>

                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField
                                    variant="outlined"
                                    size="small"
                                    placeholder="Search projects..."
                                    value={searchTerm}
                                    onChange={e => setSearchTerm(e.target.value)}
                                    InputProps={{ startAdornment: <Search color="disabled" /> }}
                                />
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            onChange={handleSelectAll}
                                            checked={processedProjects.length > 0 && selectedIds.length === processedProjects.length}
                                            indeterminate={selectedIds.length > 0 && selectedIds.length < processedProjects.length}
                                        />
                                    }
                                    label="Select All"
                                />
                                {selectedIds.length > 0 && (
                                    <Button
                                        variant="outlined"
                                        color="error"
                                        startIcon={<Delete />}
                                        onClick={() => handleDeleteRequest(selectedIds)}
                                    >
                                        Delete ({selectedIds.length})
                                    </Button>
                                )}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <Button
                                    variant="outlined"
                                    startIcon={<FilterAlt />}
                                    onClick={() => handleToggleSidebar('search')}
                                >
                                    Advanced Search
                                </Button>
                                <Button
                                    variant="outlined"
                                    startIcon={<Settings />}
                                    onClick={() => handleToggleSidebar('grid')}
                                >
                                    Table Settings
                                </Button>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(e, v) => v && setViewMode(v)}
                                >
                                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>

                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                <AdminComponents.ChartTypeSelectorContainer>
                                    <AdminComponents.StyledToggleButtonGroup
                                        value={chartType}
                                        exclusive
                                        onChange={(e, newType) => newType && setChartType(newType)}
                                        size="small"
                                        fullWidth
                                    >
                                        <ToggleButton value="bar" title="Bar Chart"><BarChart />Bar</ToggleButton>
                                        <ToggleButton value="line" title="Line Chart"><ShowChart />Line</ToggleButton>
                                        <ToggleButton value="pie" title="Pie Chart"><PieChart />Pie</ToggleButton>
                                        <ToggleButton value="doughnut" title="Doughnut Chart"><DonutLarge />Donut</ToggleButton>
                                    </AdminComponents.StyledToggleButtonGroup>
                                </AdminComponents.ChartTypeSelectorContainer>
                                <ProjectGraph project={displayProject} chartType={chartType} />
                            </AdminComponents.DetailsPane>
                        </AdminComponents.ContentBody>

                        <ActivityLog logs={activityLog} />

                    </AdminComponents.MainContentArea>

                    <Drawer
                        variant="persistent"
                        anchor="right"
                        open={isSidebarOpen}
                    >
                        <AdminComponents.SidebarContainer>
                            <AdminComponents.SidebarHeader>
                                <Typography variant="h6">
                                    {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
                                </Typography>
                                <IconButton onClick={() => setIsSidebarOpen(false)}>
                                    <Close />
                                </IconButton>
                            </AdminComponents.SidebarHeader>

                            <AdminComponents.SidebarContent>
                                {sidebarMode === 'search' && (
                                    <>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Quick Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.QuickFilterContainer>
                                                {quickFilterOptions.map(opt => (
                                                    <Chip key={opt} label={opt} onClick={() => handleAddQuickFilter(opt)} />
                                                ))}
                                            </AdminComponents.QuickFilterContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                                            <FormControl fullWidth size="small">
                                                <InputLabel>Field</InputLabel>
                                                <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                                                    {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                                                </Select>
                                            </FormControl>
                                            <FormControl fullWidth size="small">
                                                <InputLabel>Operator</InputLabel>
                                                <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                                                    {FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                                                </Select>
                                            </FormControl>
                                            <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))} />
                                            <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {stagedFilters.length > 0 ? stagedFilters.map(f => (
                                                    <Chip key={f.id} label={`${ALL_COLUMNS.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => handleRemoveStagedFilter(f.id)} />
                                                )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {activeFilters.length > 0 ? activeFilters.map(f => (
                                                    <Chip key={f.id} label={`${ALL_COLUMNS.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                                                )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                        </AdminComponents.SidebarSection>
                                    </>
                                )}
                                {sidebarMode === 'grid' && (
                                    <>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.ColumnActionContainer>
                                                <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                                                <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
                                            </AdminComponents.ColumnActionContainer>
                                            <AdminComponents.ColumnVisibilityContainer>
                                                {ALL_COLUMNS.map(col => (
                                                    <FormControlLabel
                                                        key={col.key}
                                                        control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} name={col.key} />}
                                                        label={col.label}
                                                    />
                                                ))}
                                            </AdminComponents.ColumnVisibilityContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {groupByKeys.length > 0 ? groupByKeys.map(key => (
                                                    <Chip
                                                        key={key}
                                                        label={ALL_COLUMNS.find(c => c.key === key)?.label}
                                                        onDelete={() => handleGroupByChange(key)}
                                                    />
                                                )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                            <AdminComponents.ColumnVisibilityContainer>
                                                {ALL_COLUMNS.filter(c => c.groupable).map(col => (
                                                    <FormControlLabel
                                                        key={col.key}
                                                        control={
                                                            <Checkbox
                                                                checked={groupByKeys.includes(col.key)}
                                                                onChange={() => handleGroupByChange(col.key)}
                                                            />
                                                        }
                                                        label={col.label}
                                                    />
                                                ))}
                                            </AdminComponents.ColumnVisibilityContainer>
                                        </AdminComponents.SidebarSection>
                                    </>
                                )}
                            </AdminComponents.SidebarContent>

                            <AdminComponents.SidebarFooter>
                                {sidebarMode === 'search' && (
                                    <>
                                        <Button variant="outlined" onClick={handleResetFilters}>Reset</Button>
                                        <Button variant="contained" color="primary" onClick={handleApplyFilters}>Apply</Button>
                                    </>
                                )}
                                {sidebarMode === 'grid' && (
                                    <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>
                                )}
                            </AdminComponents.SidebarFooter>
                        </AdminComponents.SidebarContainer>
                    </Drawer>

                    <ProjectDialog
                        open={modalState.isOpen}
                        onClose={handleCloseModal}
                        projectData={modalState.project}
                        mode={modalState.mode}
                        onSave={handleSaveProject}
                    />

                    <ConfirmationDialog
                        open={deleteConfirmation.isOpen}
                        onClose={handleCancelDelete}
                        onConfirm={handleConfirmDelete}
                        title="Confirm Project Deletion"
                    >
                        Are you sure you want to delete {deleteConfirmation.idsToDelete.length === 1 ? 'this project' : `these ${deleteConfirmation.idsToDelete.length} projects`}? This action cannot be undone.
                    </ConfirmationDialog>
                </AdminComponents.AppBody>
            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default Projects;